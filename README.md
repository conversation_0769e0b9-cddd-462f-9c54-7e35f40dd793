# sanatanadharma.world (Angular + NodeJS + MySQL)

## Описание

- **Фронтенд**: Angular 18
- **Бэкенд**: Node.js 
- **База данных**: MySQL


## Установка

### Необходимые инструменты

- [Docker](https://www.docker.com/get-started)
- [Docker Compose](https://docs.docker.com/compose/install/)


1. Запустите Docker Compose:
   `docker-compose up --build`

2.	После успешного запуска приложения вы сможете открыть его в браузере по следующему адресу:
	  `http://localhost:4000`


## Continuous integration
- currently we do [gitlab flow](https://github.com/jadsonjs/gitlab-flow) <a target="_blank" rel="noopener noreferrer" href="https://github.com/jadsonjs/gitlab-flow/blob/master/images/gitlab_flow.png"><img src="https://github.com/jadsonjs/gitlab-flow/raw/master/images/gitlab_flow.png" style="max-width: 100%;" width="500" align="middle"></a>
- we would like to use [dou gitlab flow](https://about.gitlab.com/blog/2023/07/27/gitlab-flow-duo/) <img src="https://about.gitlab.com/images/blogimages/gitlab-flow-duo/*********************feedback-loops.png" alt="The GitLab Flow inner and outer loops">
- all you need to do is to push your changes to master branch / merge PR to master branch
- XXX looking for the docker image updates and will restart docker seemlessly
- you can also trigger build manually `./make.sh deploy-dev`

### Resources
- [gitlab flow full documentation](https://docs.gitlab.co.jp/ee/topics/gitlab_flow.html)
- [github actions](https://docs.github.com/en/actions/about-github-actions/about-continuous-integration-with-github-actions)
- [CI/CD](https://about.gitlab.com/solutions/continuous-integration/)
- [Git best practices](https://uniandes-se4ma.gitlab.io/guides/gitFlow.html)
