.avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: -240px;

  .empty-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-image: url(../../../../../assets/images/avatar.svg);
    transition: background-image 200ms ease-in-out;
    cursor: pointer;

    &:hover {
      background-image: url(../../../../../assets/images/avatar_hover.svg);
    }
  }
}

input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

input[type="date"]::-webkit-clear-button {
  display: none;
}

input[type="date"] {
  -moz-appearance: textfield;
}

input[type="date"]::-webkit-inner-spin-button {
  display: none;
}

.profile-form {
  gap: 20px;

  .field-wrapper {
    position: relative;

    input {
      width: 450px;
      height: 50px;
      border-radius: 15px;
      outline: none;
      padding: 13px 25px;
      border: 1px solid var(--text-color);
      background: transparent;
      margin: 0 auto;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--font-color1);
    }

    &.email-display {
      .email-text {
        width: 450px;
        height: 50px;
        border-radius: 15px;
        padding: 13px 25px;
        border: 1px solid var(--text-color);
        background: transparent;
        margin: 0 auto;
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 0;
        color: var(--font-color1);
        display: flex;
        align-items: center;
        box-sizing: border-box;
      }
    }

    .sufix {
      position: absolute;
      top: 13px;
      right: 13px;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--font-color1);
    }
  }
}

.profile-form label {
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  letter-spacing: 0;
  color: var(--font-color1);
}

.save-btn {
  width: 234px;
  height: 50px;
  padding: 0;
  position: relative;
  margin: 60px auto 35px !important;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.exit-button {
  color: var(--font-color1);
  margin: 0 auto;
  background: url(../../../../../assets/images/exit_button.svg);
  cursor: pointer;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  width: 187px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

@media (max-width: 1250px) {
  .avatar {
    width: 120px;
    height: 120px;
    left: -160px;

    .empty-avatar {
      width: 120px;
      height: 120px;
      background-size: contain;
      background-position: center;
    }
  }

  .profile-form {
    gap: 12px;

    .field-wrapper {
      position: relative;

      input {
        width: 340px;
        height: 40px;
        border-radius: 10px;
        padding: 11px 15px;
        border: 1px solid var(--text-color);
        background: transparent;
        font-size: 18px;
        line-height: 20px;
      }

      &.email-display {
        .email-text {
          width: 340px;
          height: 40px;
          border-radius: 10px;
          padding: 11px 15px;
          border: 1px solid var(--text-color);
          background: transparent;
          font-size: 18px;
          line-height: 20px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
        }
      }

      .sufix {
        top: 11px;
        right: 8px;
        font-size: 18px;
        line-height: 20px;
      }
    }
  }

  .profile-form label {
    font-size: 14px;
    line-height: 14px;
    letter-spacing: 0;
  }

  .save-btn {
    margin: 58px auto 20px !important;
  }

  .exit-button {
    font-size: 17px;
    line-height: 17px;
  }
}

@media (max-width: 720px) {
  .avatar {
    position: static;
    margin: 0 auto 18px;
  }

  .profile-form {
    padding: 0 7px;

    .field-wrapper {
      input {
        width: 330px;
        max-width: 100%;
        height: 36px;
        font-size: 14px;
        line-height: 16px;
      }

      &.email-display {
        .email-text {
          width: 330px;
          max-width: 100%;
          height: 36px;
          font-size: 14px;
          line-height: 16px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
        }
      }

      .sufix {
        right: 11px;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }

  .profile-form label {
    font-size: 11px;
    line-height: 14px;
    letter-spacing: 0;
  }

  .save-btn {
    margin: 68px auto 20px !important;
  }

  .exit-button {
    font-size: 17px;
    line-height: 17px;
  }
}
