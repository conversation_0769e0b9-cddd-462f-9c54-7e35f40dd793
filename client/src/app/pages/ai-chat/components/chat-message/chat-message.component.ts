import { AiChatService } from '@/services/ai-chat.service'
import { CommonModule } from '@angular/common'
import { AfterViewInit, ChangeDetectionStrategy, Component, effect, ElementRef, inject, input, OnDestroy, output, signal, ViewEncapsulation } from '@angular/core'
import { ChatMessage, ChatSource } from '../../ai-chat.component'

@Component({
  selector: 'app-chat-message',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './chat-message.component.html',
  styleUrl: './chat-message.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ChatMessageComponent implements OnDestroy, AfterViewInit {
  // Inputs
  message = input.required<ChatMessage>();
  isLoading = input<boolean>(false);

  // Outputs
  sourceClick = output<ChatSource>();
  typewritingComplete = output<string>();

  // State
  isCopied = signal<boolean>(false);
  displayedContent = signal<string>('');
  private typewriterInterval?: number;
  private elementRef = inject(ElementRef);
  private aiChatService = inject(AiChatService);

  constructor() {
    effect(() => {
      const message = this.message();
      if (message.isTypewriting && message.fullContent) {
        this.startTypewriter(message.fullContent);
      } else {
        this.displayedContent.set(message.content);
        setTimeout(() => this.setupLinkClickHandlers(), 0);
      }
    });
  }

  ngAfterViewInit() {
    this.setupLinkClickHandlers();
  }

  ngOnDestroy() {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }
    this.removeLinkClickHandlers();
  }

  private setupLinkClickHandlers() {
    this.removeLinkClickHandlers();

    this.elementRef.nativeElement.addEventListener('click', this.handleContainerClick.bind(this));
  }

  private removeLinkClickHandlers() {
    this.elementRef.nativeElement.removeEventListener('click', this.handleContainerClick.bind(this));
  }

  private handleContainerClick(event: Event) {
    const target = event.target as HTMLElement;

    if (target.tagName === 'A' && target.getAttribute('href')) {
      event.preventDefault();
      const href = target.getAttribute('href')!;
      const title = target.textContent || href;

      this.loadAndShowUrlContent(href, title);
    } else if (target.classList.contains('message-link')) {
      event.preventDefault();
      const url = target.getAttribute('data-url');
      const title = target.getAttribute('data-title');

      if (url && title) {
        this.loadAndShowUrlContent(url, title);
      }
    }
  }

  private loadAndShowUrlContent(url: string, title: string) {
    const loadingSource: ChatSource = {
      id: url,
      title: title,
      content: 'Загружаем контент...',
      url: url
    };

    this.onSourceClick(loadingSource);

    this.aiChatService.fetchUrlContent(url).subscribe(urlContent => {
      const source: ChatSource = {
        id: url,
        title: urlContent.title,
        content: urlContent.content,
        url: url
      };

      this.onSourceClick(source);
    });
  }



  private startTypewriter(fullText: string) {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }

    let currentIndex = 0;
    this.displayedContent.set('');

    this.typewriterInterval = window.setInterval(() => {
      if (currentIndex < fullText.length) {
        this.displayedContent.update(current => current + fullText[currentIndex]);
        currentIndex++;
      } else {
        if (this.typewriterInterval) {
          clearInterval(this.typewriterInterval);
          this.typewriterInterval = undefined;
        }
        this.typewritingComplete.emit(this.message().id);

        setTimeout(() => this.setupLinkClickHandlers(), 0);
      }
    }, 30);
  }

  onCopyMessage() {
    const message = this.message();
    const content = message.fullContent || message.content;
    if (!content.trim()) return;

    navigator.clipboard.writeText(content).then(() => {
      this.isCopied.set(true);
      setTimeout(() => {
        this.isCopied.set(false);
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }

  onSourceClick(source: ChatSource) {
    this.sourceClick.emit(source);
  }

  formatTime(date: Date): string {
    return new Date(date).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Parse message content to handle markdown and citations
  parseContent(content: string): { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] {
    if (!content) return [];

    const parts: { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] = [];
    const sources = this.message().sources || [];
    
    // Simple parsing for citations [1], [2], etc.
    const citationRegex = /\[(\d+)\]/g;
    let lastIndex = 0;
    let match;

    while ((match = citationRegex.exec(content)) !== null) {
      // Add text before citation
      if (match.index > lastIndex) {
        const textContent = content.slice(lastIndex, match.index);
        if (textContent) {
          parts.push({ type: 'text', content: textContent });
        }
      }

      // Add citation
      const citationNumber = parseInt(match[1]) - 1;
      const source = sources[citationNumber];
      if (source) {
        parts.push({ 
          type: 'citation', 
          content: match[0],
          source: source
        });
      } else {
        parts.push({ type: 'text', content: match[0] });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const remainingContent = content.slice(lastIndex);
      if (remainingContent) {
        parts.push({ type: 'text', content: remainingContent });
      }
    }

    return parts.length > 0 ? parts : [{ type: 'text', content: content }];
  }

  formatMarkdown(text: string): string {
    return text
      // Bold text **text**
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text *text*
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code blocks ```code```
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      // Inline code `code`
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<span class="message-link" data-url="$2" data-title="$1">$1</span>')
      // Line breaks
      .replace(/\n/g, '<br>');
  }
}
