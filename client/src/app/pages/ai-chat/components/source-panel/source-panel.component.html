<div class="source-panel" [class.panel-open]="isOpen()">
  @if (hasSource() && source()) {
    <!-- Panel Header -->
    <div class="panel-header">
      <div class="header-content">
        <h3 class="source-title">{{ source()!.title }}</h3>
        @if (source()!.url) {
          <a 
            class="source-url" 
            [href]="source()!.url" 
            target="_blank" 
            rel="noopener noreferrer"
          >
            <span class="url-icon">🔗</span>
            <span class="url-text">Открыть источник</span>
          </a>
        }
      </div>
      <button 
        class="close-btn"
        (click)="onClose()"
        title="Закрыть панель"
      >
        <span class="close-icon">✕</span>
      </button>
    </div>

    <!-- Panel Content -->
    <div class="panel-content">
      <div class="source-content" (click)="scrollToHighlight()">
        @if (isLoading()) {
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Загружаем контент...</div>
          </div>
        } @else if (highlightedContent()) {
          <div
            class="content-text"
            [innerHTML]="formatContent(highlightedContent())"
          ></div>
        } @else {
          <div
            class="content-text"
            [innerHTML]="formatContent(source()!.content)"
          ></div>
        }
      </div>
    </div>

    <!-- Panel Footer -->
    <div class="panel-footer">
      <div class="footer-info">
        <span class="info-text">Нажмите на выделенный текст для прокрутки</span>
      </div>
    </div>
  } @else {
    <!-- Empty State -->
    <div class="empty-panel">
      <div class="empty-content">
        <div class="empty-icon">📄</div>
        <div class="empty-title">Источник не выбран</div>
        <div class="empty-description">
          Нажмите на ссылку-цитату в сообщении, чтобы просмотреть источник
        </div>
      </div>
    </div>
  }

  <!-- Mobile Backdrop -->
  <div class="mobile-backdrop" (click)="onBackdropClick($event)"></div>
</div>
