@use "../../../../styles/core.scss" as core;

.source-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--side_back);
  color: var(--font-color1);
  position: relative;
  overflow: hidden;
}

// Panel Header
.panel-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 28px;
  border-bottom: 2px solid var(--border);
  background: linear-gradient(135deg, var(--light-color) 0%, var(--side_back) 100%);
  gap: 20px;
  box-shadow: 0 2px 8px rgba(83, 46, 0, 0.1);
}

.header-content {
  flex: 1;
  min-width: 0;
}

.source-title {
  font-family: Prata, serif;
  font-size: 20px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 12px 0;
  line-height: 1.3;
  word-wrap: break-word;
  text-shadow: 0 1px 2px rgba(83, 46, 0, 0.1);
}

.source-url {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--button_);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid var(--border);
  border-radius: 10px;
  color: var(--font-color1);
  text-decoration: none;
  font-family: IBM_Plex_Sans, sans-serif;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover {
    background: var(--button_figure);
    background-size: cover;
    color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(83, 46, 0, 0.25);
  }
}

.url-icon {
  font-size: 14px;
}

.url-text {
  white-space: nowrap;
  font-weight: 500;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid var(--border);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover {
    background: var(--selection);
    border-color: core.$light2;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(83, 46, 0, 0.25);
  }
}

.close-icon {
  font-size: 18px;
  color: var(--font-color1);
  opacity: 0.8;
}

// Panel Content
.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light-color);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid rgba(42, 124, 187, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--font-color2);
  font-size: 14px;
  font-family: IBM_Plex_Sans, sans-serif;
}

.source-content {
  padding: 24px;
}

.content-text {
  font-family: IBM_Plex_Sans;
  font-size: 15px;
  line-height: 1.6;
  color: var(--font-color1);

  p {
    margin: 0 0 16px 0;
    text-align: justify;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Highlighted text styling
  :global(.highlight) {
    background: rgba(222, 165, 61, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
    color: var(--font-color1);
    border: 1px solid rgba(222, 165, 61, 0.5);
    animation: highlight-pulse 2s ease-in-out;
  }

  // Text formatting
  strong {
    font-weight: 600;
    color: var(--font-color1);
  }

  em {
    font-style: italic;
  }

  // Links in content
  a {
    color: rgba(42, 124, 187, 1);
    text-decoration: underline;

    &:hover {
      color: rgba(105, 77, 164, 1);
    }

    &:visited {
      color: rgba(105, 77, 164, 1);
    }
  }
}

@keyframes highlight-pulse {
  0% {
    background: rgba(222, 165, 61, 0.5);
    transform: scale(1);
  }
  50% {
    background: rgba(222, 165, 61, 0.7);
    transform: scale(1.02);
  }
  100% {
    background: rgba(222, 165, 61, 0.3);
    transform: scale(1);
  }
}

// Panel Footer
.panel-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border);
  background: var(--selection);
}

.footer-info {
  text-align: center;
}

.info-text {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
}

// Empty Panel
.empty-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 24px;
}

.empty-content {
  text-align: center;
  max-width: 280px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-family: Prata;
  font-size: 18px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 12px 0;
}

.empty-description {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
  opacity: 0.7;
}

// Mobile Backdrop
.mobile-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

// Mobile Styles
@media (max-width: 1024px) {
  .source-panel {
    position: fixed;
    top: 80px; // Header height
    right: 0;
    bottom: 0; // Go to bottom of screen
    z-index: 105;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);

    &.panel-open {
      .mobile-backdrop {
        display: block;
      }
    }
  }

  .mobile-backdrop {
    z-index: -1;
  }
}

@media (max-width: 768px) {
  .panel-header {
    padding: 16px 20px;
  }

  .source-title {
    font-size: 16px;
  }

  .source-url {
    padding: 5px 10px;
    font-size: 12px;
  }

  .close-btn {
    width: 28px;
    height: 28px;
  }

  .close-icon {
    font-size: 14px;
  }

  .source-content {
    padding: 20px;
  }

  .content-text {
    font-size: 14px;
    line-height: 1.5;
  }

  .panel-footer {
    padding: 12px 20px;
  }

  .info-text {
    font-size: 11px;
  }

  .empty-panel {
    padding: 30px 20px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }
}
