import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { Component, inject, OnInit, PLATFORM_ID, ViewEncapsulation } from "@angular/core";
import {environment} from "@/env/environment";
import { AdvertisingService } from "@/services/advertising.service";

interface MonthGroup {
  monthYear: string;
  advertisements: any[];
}

@Component({
    selector: 'app-calendar',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage
    ],
    encapsulation: ViewEncapsulation.None,  // Отключает изоляцию стилей
    templateUrl: './calendar.component.html',
    styleUrl: './calendar.component.scss'
  })
  export class CalendarComponent implements OnInit {
    protected readonly environment = environment;
    advertisingService = inject(AdvertisingService);
    platformId = inject(PLATFORM_ID);
   
  
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            this.advertisingService.getAll().subscribe(
                {
                    next: (res: any) => {
                    // Фильтруем только неактивную рекламу (active: null или false)
                    const inactiveAds = res.filter((ad: any) => ad.active === null || ad.active === false);
                    
                    // Группируем по месяцам
                    this.groupedAdvertisements = this.groupByMonth(inactiveAds);

                    },
                    error: (err) => {
                        console.error('Error loading advertisements:', err);
                    }
                }
            );
        }
    }

    
  groupedAdvertisements: MonthGroup[] = [];

  /**
   * Группирует рекламу по месяцам
   */
  private groupByMonth(advertisements: any[]): MonthGroup[] {
    const groups: { [key: string]: any[] } = {};
    
    advertisements.forEach(ad => {
      if (ad.date) {
        const date = new Date(ad.date);
        const monthYear = date.toLocaleDateString('ru-RU', { 
          year: 'numeric', 
          month: 'long' 
        });
        
        if (!groups[monthYear]) {
          groups[monthYear] = [];
        }
        groups[monthYear].push(ad);
      }
    });

    // Преобразуем в массив и сортируем по дате (более ранние сверху)
    return Object.keys(groups)
      .sort((a, b) => {
        const dateA = new Date(groups[a][0].date);
        const dateB = new Date(groups[b][0].date);
        return dateA.getTime() - dateB.getTime();
      })
      .map(monthYear => ({
        monthYear,
        advertisements: groups[monthYear].sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateA.getTime() - dateB.getTime();
        })
      }));
  }

  navigateToLink(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link, '_blank');
    }
  }

  onImageError(event: any) {
    // Hide only the image, show placeholder instead
    const imgElement = event.target;
    imgElement.style.display = 'none';
    
    // Find the placeholder and show it
    const container = imgElement.closest('.ad-image-container');
    if (container) {
      const placeholder = container.querySelector('.ad-image-placeholder');
      if (placeholder) {
        placeholder.style.display = 'flex';
      }
    }
  }
}
