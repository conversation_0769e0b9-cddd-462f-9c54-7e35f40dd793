import { AccentCardComponent } from '@/components/cards/accent-card/accent-card.component';
import { CardWithDomeComponent } from '@/components/cards/card-with-dome/card-with-dome.component';
import { ChronologicalCardComponent } from '@/components/cards/chronological-card/chronological-card.component';
import { CarouselV2Component } from '@/components/carousel-v2/carousel-v2.component';
import { Component, inject, PLATFORM_ID, HostListener, signal } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Levels } from '@/enums/levels.enum';
import { JoinCardComponent } from '@/components/cards/join-card/join-card.component';
import { DailySidebarComponent } from '@/components/daily-sidebar/daily-sidebar.component';
import { HeaderV2Component } from '@/components/header-v2/header-v2.component';

@Component({
  selector: 'app-main-v2',
  imports: [CarouselV2Component, ChronologicalCardComponent, AccentCardComponent, CardWithDomeComponent, JoinCardComponent, DailySidebarComponent,
    HeaderV2Component

  ],
  templateUrl: './main-v2.component.html',
  standalone: true,
  styleUrl: './main-v2.component.scss'
})
export class MainV2Component {
  itemsPerView = 4;

  private platformId = inject(PLATFORM_ID);

  isScrolled = false;
  isDarkMarker = false;

  events = [
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    }
  ];

  courses = [
   {
      name: 'Основы медитации',
      description: 'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ENTRY
    },
    {
      name: 'Йога сутры Патанджали',
      description: 'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.INTERMEDIATE
    },
    {
      name: 'Адвайта Веданта',
      description: 'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ADVANCED
    },
    {
      name: 'Основы медитации',
      description: 'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ENTRY
    },
    {
      name: 'Основы медитации',
      description: 'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ENTRY
    }
  ];

  joinCards = [
   {
      name: 'Принять Символ Веры',
      description: 'Вступление в традицию',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_Символ_Веры.webp',
    },
    {
      name: 'Принять дикшу',
      description: 'Посвящение',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_дикшу.webp',
    },
    {
      name: 'Стать монахом',
      description: 'Жизнь Садху',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Стать_монахом.webp',
    },
    {
      name: 'Приехать в Ашрам',
      description: 'Участие в ретрите и служении',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Приехать_в_Ашрам.webp',
    },
    {
      name: 'Заказать ритуал',
      description: 'Поддержка и благословение',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Заказать_ритуал.webp',
    },
    {
      name: 'Создать Ашрам / Центр',
      description: 'Расширение миссии',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Центр.webp',
    }
  ];

  projectTabs = [
    { title: 'Образовательная программа', 
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      link: '/education'
    },
    { title: 'Экологическая инициатива', 
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      link: '/education'
    },
    { title: 'Гуманитарная помощь', 
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      link: '/education'
    },
    { title: 'Институт Васиштхи', 
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      link: '/education'
    },
  ];

  activeProjectTab = this.projectTabs[0];

  setActiveProjectTab(tab: any): void {
    this.activeProjectTab = tab;
  }

  @HostListener('window:resize')
  onResize(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.checkScreenSize();
    }
  }
    
    @HostListener('window:scroll', [])
    private checkScroll(): void {
      if (isPlatformBrowser(this.platformId)) {
        this.isScrolled = window.scrollY > 20;
        const halfHeightScreen = window.innerHeight / 2;
        this.isDarkMarker = window.scrollY > halfHeightScreen;
      }
    }
    
    ngOnInit(): void {
    this.checkScroll();
    this.checkScreenSize();
  }

  checkScreenSize(): void {
    if (isPlatformBrowser(this.platformId)) {
      const width = window.innerWidth;
      if (width < 560) {
        this.itemsPerView = 2;
      } else if (width < 920) {
        this.itemsPerView = 3;
      } else {
        this.itemsPerView = 4;
      }
    }
  }
}
