import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component'
import { CustomDropdownComponent } from '@/components/custom-dropdown/custom-dropdown.component'
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component"
import { ListIconComponent } from "@/components/svg-components/list-icon/list-icon.component"
import { Track } from '@/interfaces/track'
import { IsInPlaylistPipe } from '@/pipes/isInPlaylist.pipe'
import { AudioService } from "@/services/audio.service"
import { AuthService } from '@/services/auth.service'
import { PlaylistService } from "@/services/playlist.service"
import { ProfileService } from "@/services/profile.service"
import { ShareDataService } from '@/services/share-data.service'
import { ToasterService } from '@/services/toaster.service'
import { formatContentCountMessage } from '@/utils/pluralization.util'
import { CommonModule, Location, NgOptimizedImage } from '@angular/common'
import { Component, DestroyRef, effect, ElementRef, HostListener, inject, OnInit, PLATFORM_ID, ViewChild } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { TranslocoService } from "@jsverse/transloco"
import { NgSelectModule } from "@ng-select/ng-select"
import moment from "moment/moment"
import { debounceTime, distinctUntilChanged, Subject, switchMap, tap } from 'rxjs'
import { PlaylistDialogComponent } from './playlist-dialog/playlist-dialog.component'

@Component({
  selector: 'app-audio-gallery',
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    NgSelectModule,
    NgOptimizedImage,
    BreadcrumbComponent,
    CustomDropdownComponent,
    ReactiveFormsModule,
    IsInPlaylistPipe,
    PlaylistDialogComponent, ListIconComponent, FavoritesIconComponent],
  templateUrl: './audio-gallery.component.html',
  styleUrls: ['./audio-gallery.component.scss']
})
export class AudioGalleryComponent implements OnInit {
  @HostListener('document:click', ['$event'])
  closeDropdown(event: Event) {
    const targetElement = event.target as HTMLElement;
    const dropdown = document.querySelector('.dropdown');
    if (dropdown && !dropdown.contains(targetElement)) {
      this.dropdownOpen = false;
    }
  }
  @HostListener('document:mousedown', ['$event'])
  onClickOutside(event: MouseEvent) {
    const modal = this.modal?.nativeElement as HTMLDialogElement;
    if (modal && modal.open) {
      const dialogContent = modal.querySelector('.cont_mod');
      if (dialogContent && !dialogContent.contains(event.target as Node) &&
          !(event.target as HTMLElement).classList.contains('x_bt')) {
        this.closeModal(modal);
      }
    }
  }
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  searchSubject = new Subject<any>();
  toasterService = inject(ToasterService);
  shareDataService = inject(ShareDataService);
  translocoService = inject(TranslocoService);
  playlistService = inject(PlaylistService);
  profileService = inject(ProfileService);
  audioService = inject(AudioService);
  route = inject(ActivatedRoute);
  platformId = inject(PLATFORM_ID)
  authService = inject(AuthService);
  router = inject(Router);
  location = inject(Location);
  fb = inject(FormBuilder)
  filter: FormGroup = this.fb.group({
    search: '',
    category: '',
    sortOrder: 'dateDesc',
    tag: [],
    page: 1
  })
  filters: any = {
    status: '',
    startDate: '',
    endDate: '',
    comment: '',
    description: '',
    // type: '',
    videoStatus: '',
    sortOrder: 'dateDesc',
    page: 1,
    youtube: 1,
    tags: [],
    format: [],
    author: [],
    durationFrom: null,
    durationTo: null,
    years: []
  };
  // typesArray: any;
  sortOptions = [
    { label: 'Алфавиту', value: 'title', id: 0 },
    { label: 'Дате', value: 'date', id: 1 },
    { label: 'Популярности', value: 'views', id: 2 },
    { label: 'Лайкам', value: 'likes', id: 3 },
    { label: 'Длительности', value: 'duration', id: 4 },
  ];
  sortDirection: 'Asc' | 'Desc' = 'Desc';
  currentSortField: string = 'date';
  selectedSortLabel = 'Дате';
  showPlaylist = false;
  videosArray: any;
  audioArray: any;
  authorsArray: any[] = [];
  selectedTrackId: number = -1;
  selectedPlaylists: any[] = []
  playlistsToRemoveFrom: any[] = []
  playlists: any[] = [];
  currentPage: number = 1
  itemsPerPage: number = 10;
  dropdownOpen = false;
  dropdownSortOpen = false;
  show_menu: Record<number, boolean> = {};

  isOpened: Record<number, boolean> = {};
  data: any = [];
  selectedTags: any[] = [];
  selectedYears: any[] = [];
  selectedAuthors: <AUTHORS>
  private readonly destroyRef = inject(DestroyRef);

  constructor() {
    effect(() => {
      if (this.audioService.data()?.items?.length > 0) {
        const newItems = this.audioService.data().items;

        if (!this.data.length) {
          this.data = [...newItems];
        } else {
          const updatedData = [...this.data];

          const existingItemsMap = new Map(
            updatedData.map((item: any, index: number) => [item.id, index])
          );

          newItems.forEach((newItem: any) => {
            const existingIndex = existingItemsMap.get(newItem.id);

            if (existingIndex !== undefined) {
              updatedData[existingIndex] = newItem;
            } else {
              updatedData.push(newItem);
            }
          });

          this.data = updatedData;
        }
      }
    });
  }

  get formatDate() {
    return (date: any) => moment(date).format('YYYY.MM.DD');
  }

  ngOnInit() {
    this.audioService.getTags().subscribe(() => {
      // Update tag checkboxes after tags are loaded
      this.updateTagCheckboxes();
    });

    this.profileService.getProfile().subscribe();

    this.route.queryParams.pipe(
      takeUntilDestroyed(this.destroyRef),
      debounceTime(300),
      tap((params: any) => {
        // Initialize empty arrays for collection parameters
        this.filters.tags = [];
        this.filters.author = [];
        this.filters.format = [];

        // Process query parameters and update filters
        Object.keys(params).forEach(key => {
          if (key === 'tags' || key === 'author' || key === 'format') {
            // Always convert to array, handling both string and array cases
            if (params[key]) {
              this.filters[key] = Array.isArray(params[key])
                ? params[key].map(String)  // Ensure all values are strings
                : [String(params[key])];   // Single value as array
            }
          } else if (key === 'page' || key === 'durationFrom' || key === 'durationTo') {
            // Convert numeric parameters
            this.filters[key] = Number(params[key]);
          } else {
            // Copy other parameters directly
            this.filters[key] = params[key];
          }
        });

        if(this.route.snapshot.data['video']) this.filters.youtube = 2;

        if (this.filters.sortOrder) {
          this.updateSortUIFromFilters();
        } else {
          const sortValue = this.filter.get('sortOrder')?.value || 'dateDesc';
          const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
          this.currentSortField = match?.[1] || 'date';
          this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Desc';
          this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || '';
          this.filters.sortOrder = this.currentSortField + this.sortDirection;
        }

        if(this.filters.tags.length) {
          this.selectedTags = this.audioService.tags.filter((e: any) => this.filters.tags.includes(e.id.toString()))
        }

        if(this.filters.author.length) {
          setTimeout(() => {
            this.selectedAuthors = this.authorsArray.filter((e: any) => this.filters.author.includes(e.id.toString()))
          }, 500)
        }

        // Update internal filters but don't navigate to avoid loops
        this.audioService.filters = {...this.filters};

        // Update UI checkbox states
        this.updateTagCheckboxes();
        this.updateAuthorCheckboxes(); // Add this line
      }),
      switchMap(() =>
         this.audioService.getAll()
        )
    ).subscribe(({authors, types, statuses, items, videoStatuses}: any) => {
      // this.typesArray = types;
      this.videosArray = videoStatuses;
      this.audioArray = statuses;
      this.authorsArray = authors;

      // Call update method after data is loaded
      this.updateAuthorCheckboxes();
      this.updateTagCheckboxes();
    });

    this.searchSubject.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe((e) => {
      this.applyFilter();
    });

    // Removed automatic toaster subscription - toaster now only shows on button clicks and outside clicks

    //this.getPleylists();
  }

  applyFilter(loadMore: boolean = false) {
    this.data = [];

    this.filters = {
      ...this.filters,
      ...this.filter.value
    };

    console.log(this.filters);

    this.updateUrlWithFilters();
  }

  updateUrlWithFilters() {
    const queryParams: any = {};

    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];

      if (value === null || value === undefined || value === '' || value === 'null') {
        return;
      }

      if (Array.isArray(value) && value.length === 0) {
        return;
      }

      if (Array.isArray(value) && value.length > 0) {
        queryParams[key] = value.map(item => item.toString());
      } else if ((key === 'durationFrom' || key === 'durationTo') && (value === null || value === 'null')) {
        return;
      } else {
        queryParams[key] = value;
      }
    });

    const url = this.router.createUrlTree([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
    }).toString();

    this.location.replaceState(url);

    this.audioService.filters = {...this.filters};
    this.audioService.getAll().subscribe();
  }

  openModal() {
    this.modal.nativeElement.showModal();
  }

  toggleDropdownSort() {
    this.dropdownSortOpen = !this.dropdownSortOpen;
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
    this.showFilterResultsToast();
  }

  // selectSort(value: string) {
  //   this.filter.patchValue({ sort: value });
  //   this.selectedSortLabel = this.sortOptions.find(option => option.value === value)?.label || 'По названию';
  //   this.dropdownOpen = false;
  //   this.applyFilter();
  // }

  getPleylists() {
    if(this.authService.isAuth) {
    this.profileService.getPlaylist().subscribe((res: any) => this.playlists = res);
    }
  }

  changeTagFilter(tags: any) {
    this.data = [];
    const tagsCopy = tags && tags.length > 0
      ? tags.map((tag: any) => tag.id.toString())
      : [];

    this.filters.tags = tagsCopy;
    this.filters.page = 1;
    this.updateUrlWithFilters();
  }

  changeAuthorFilter(authors: any) {
    this.data = [];
    const tagsCopy = authors && authors.length > 0
      ? authors.map((author: any) => author.id.toString())
      : [];

    this.filters.author = tagsCopy;
    this.filters.page = 1;
    this.updateUrlWithFilters();
  }


  updateAuthorCheckboxes() {
    if (this.authorsArray && this.filters.author) {
      this.authorsArray.forEach((author: any) => {
        // Set checked property based on if the author name is in the filters
        author.checked = this.filters.author.includes(author.name);
      });
    }
  }


  // Add this method to explicitly update tag checkbox states based on filter values
  updateTagCheckboxes() {
    if (this.audioService.tags && this.filters.tags) {
      this.audioService.tags.forEach((tag: any) => {
        // Set checked property based on if the tag ID is in the filters
        tag.checked = this.filters.tags.includes(tag.id.toString());
      });
    }
  }


  updateSortUIFromFilters() {
    if (this.filters.sortOrder) {
      const sortValue = this.filters.sortOrder;
      const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
      this.currentSortField = match?.[1] || 'date';
      this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Asc';
      this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || '';

      this.filter.patchValue({ sortOrder: this.filters.sortOrder });
    }
  }

  updateUIFromFilters() {
    // Update tag checkboxes
    if (this.audioService.tags && this.audioService.tags.length > 0) {
      this.audioService.tags.forEach((tag: any) => {
        // Set checked property based on if the tag ID is in the filters
        tag.checked = this.filters.tags.includes(tag.id.toString()) ||
                     this.filters.tags.includes(tag.id);
      });
    }

    // Update author checkboxes
    if (this.authorsArray && this.authorsArray.length > 0) {
      this.authorsArray.forEach((author: any) => {
        // Set checked property based on if the author name is in the filters
        author.checked = this.filters.author.includes(author.name);
      });
    }
  }

  playTrack(track: Track): any {
    if(!this.audioService.hasAccess(track)) {
      this.toasterService.showToast('Платный материал, оформите подписку', 'error', 'bottom-middle', 3000)
      return false;
    }
    this.shareDataService.addToPlaylist(track, true);
  }

  openLecture(track: any, tab?: 'text' | 'video') {
    if(tab === 'text') {
      this.getAbsolutePath(track.text_link)
      return;
    }

    const queryParams = tab ? { tab } : {};

    this.router.navigate(
      [this.translocoService.getActiveLang(), 'audiogallery', 'audiolektsii', track.external_id],
      { queryParams }
    );
  }

  addToPlaylist(track: Track): any {
    if(!this.audioService.hasAccess(track)) {
      this.toasterService.showToast('Платный материал, оформите подписку', 'error', 'bottom-middle', 3000)
      return false;
    }

    this.shareDataService.addToPlaylist(track);
    this.toasterService.showToast('Лекция добавлена в очередь', 'success', 'bottom-middle', 3000);
  }

  share(track: any) {
    navigator.clipboard.writeText('https://dev.advayta.org/' + this.router.url + '/' + track.external_id).then(() => {
      this.toasterService.showToast('Ссылка на аудио скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
    }).catch((error) => {
      console.error('Unable to copy text to clipboard', error);
    });
  }

  showPlaylistDialog(id: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.showPlaylist = true;

    this.selectedTrackId = id;
  }

  playlistClosed(event: any) {
    this.showPlaylist = event;
  }

  playlistSelected(playlists: any) {
    this.showPlaylist = false;

    this.playlists = playlists;
  }

  like(id: number) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.audioService.like(id).subscribe((r) => {

      const trackIndex = this.data.findIndex((track: any) => track.id === id);
      if (trackIndex !== -1) {
        this.data[trackIndex].liked = !this.data[trackIndex].liked;
      }
      if(this.data[trackIndex].liked) {
        this.data[trackIndex].likes++;
      } else {
        this.data[trackIndex].likes--;
      }

      // if((r as any).audio) {
      //   this.toasterService.showToast('Лекция добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
      // } else {
      //   this.toasterService.showToast('Лекция удалена из понравившихся!', 'success', 'bottom-middle', 3000);
      // }
    })
  }

  addToFavourites(trackId: number) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    return this.audioService.addToFavourites(trackId).subscribe((r) => {
      if((r as any).audio) {
        this.toasterService.showToast('Лекция добавлена в избранное!', 'success', 'bottom-middle', 3000);
      } else {
        this.toasterService.showToast('Лекция удалена из избранного!', 'success', 'bottom-middle', 3000);
      }
    })
  }



  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  formatOptions = [
    { name: 'Лекция с текстом', value: 'text', id: 0 },
    { name: 'Лекция с видео', value: 'video', id: 1 },
    { name: 'Платная лекция', value: 'paid', id: 2 },
  ]

  dataOptions = [
    { name: 'Все', value: 'all', id: 0 },
    { name: '2004', value: '2004-01-01', id: 1 },
    { name: '2005', value: '2005-01-01', id: 2 },
    { name: '2006', value: '2006-01-01', id: 3 },
    { name: '2007', value: '2007-01-01', id: 4 },
    { name: '2008', value: '2008-01-01', id: 5 },
    { name: '2009', value: '2009-01-01', id: 6 },
    { name: '2010', value: '2010-01-01', id: 7 },
    { name: '2011', value: '2011-01-01', id: 8 },
    { name: '2012', value: '2012-01-01', id: 9 },
    { name: '2013', value: '2013-01-01', id: 10 },
    { name: '2014', value: '2014-01-01', id: 11 },
    { name: '2015', value: '2015-01-01', id: 12 },
    { name: '2016', value: '2016-01-01', id: 13 },
    { name: '2017', value: '2017-01-01', id: 14 },
    { name: '2018', value: '2018-01-01', id: 15 },
    { name: '2019', value: '2019-01-01', id: 16 },
    { name: '2020', value: '2020-01-01', id: 17 },
    { name: '2021', value: '2021-01-01', id: 18 },
    { name: '2022', value: '2022-01-01', id: 19 },
    { name: '2023', value: '2023-01-01', id: 20 },
    { name: '2024', value: '2024-01-01', id: 21 },
    { name: '2025', value: '2025-01-01', id: 22 }
  ]

  changeFormatFilter(event: any) {
    this.data = [];

    if (Array.isArray(event)) {
      // Reset the format filter first
      this.filters.format = [];

      // Add all selected format values to the filter
      if (event.length > 0) {
        event.forEach(item => {
          this.filters.format.push(item.value);
        });
      }
    } else {
      // Original logic for handling single checkbox events
      const checked = (event.target as HTMLInputElement).checked;
      const value = event.value || event.target.value;

      const index = this.filters.format.indexOf(value);
      if (!checked) {
        if (index !== -1) this.filters.format.splice(index, 1);
      } else {
        if (index === -1) this.filters.format.push(value);
      }
    }

    this.filters.page = 1;

    // Apply the updated filters
    this.updateUrlWithFilters();
  }

  setCalendar(e: any) {
    this.data = [];
    this.filters.years = [];

    if (Array.isArray(e) && e.length > 0) {
      if (e.some(item => item.value === 'all')) {
        this.filters.years = [];
      } else {
        this.filters.years = e.map(item => item.name);
      }
    } else {
      this.filters.years = [];
    }

    this.filters.page = 1;
    this.updateUrlWithFilters();
  }

  selectSort(field: string) {
    this.data = [];
    if (this.currentSortField === field) {
      this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
    } else {
      this.currentSortField = field;
      this.sortDirection = 'Asc';
    }

    const sortOrder = this.currentSortField + this.sortDirection;
    this.filter.patchValue({ sortOrder });
    this.filters.sortOrder = sortOrder;
    this.selectedSortLabel = this.sortOptions.find(option => option.value === field)?.label || '';

    this.updateUrlWithFilters();
  }

  nextPage() {
    if(this.filters.page < this.audioService.total) {
      this.filters.page++;
      this.updateUrlWithFilters();
    }
  }

  previousPage() {
    if (this.filters.page > 1) {
      this.filters.page--;
      this.updateUrlWithFilters();
    }
  }

  resetFilters() {
    // Reset filters with proper types
    const defaultYoutube = this.route.snapshot.data['video'] ? 2 : 1;
    this.filters = {
      status: '',
      startDate: '',
      endDate: '',
      comment: '',
      description: '',
      videoStatus: '',
      sortOrder: 'dateDesc',
      page: 1,
      youtube: defaultYoutube,
      tags: [],
      format: [],
      author: [],
      durationFrom: null,
      durationTo: null,
      years: []
    };
    this.selectedTags = [];
    this.selectedYears = [];
    this.selectedAuthors = [];
    this.data = [];

    // Update UI checkboxes
    this.updateTagCheckboxes();
    this.updateAuthorCheckboxes();

    // Update audio service filters
    this.audioService.filters = {...this.filters};

    const url = this.router.createUrlTree([], {
      relativeTo: this.route,
      queryParams: {},
    }).toString();

    this.location.replaceState(url);

    this.audioService.filters = {...this.filters};
    this.audioService.getAll().subscribe();
  }

  resetAndCloseModal(modal: HTMLDialogElement) {
    // Reset filters with proper types
    const defaultYoutube = this.route.snapshot.data['video'] ? 2 : 1;
    this.filters = {
      status: '',
      startDate: '',
      endDate: '',
      comment: '',
      description: '',
      videoStatus: '',
      sortOrder: 'dateDesc',
      page: 1,
      youtube: defaultYoutube,
      tags: [],
      format: [],
      author: [],
      durationFrom: null,
      durationTo: null,
      years: []
    };
    this.selectedTags = [];
    this.selectedYears = [];
    this.selectedAuthors = [];
    this.data = [];

    // Update UI checkboxes
    this.updateTagCheckboxes();
    this.updateAuthorCheckboxes();

    // Update audio service filters
    this.audioService.filters = {...this.filters};

    const url = this.router.createUrlTree([], {
      relativeTo: this.route,
      queryParams: {},
    }).toString();

    this.location.replaceState(url);

    this.audioService.filters = {...this.filters};
    this.audioService.getAll().subscribe(() => {
      this.showFilterResultsToast();
      modal.close();
    });
  }


  getAbsolutePath(url: string): void {
    if (!url) return;

    try {
      // Extract the path from the full URL
      const urlObj = new URL(url);
      // Get the pathname (preserving the leading slash)
      const pathname = urlObj.pathname;

      // Perform the redirect using Angular Router
      this.router.navigateByUrl(pathname);
    } catch (e) {
      console.error('Invalid URL:', url);
    }
  }

  showMenu(index: number, event: Event) {
    event.stopPropagation();

    this.show_menu = {};
    this.show_menu[index] = !this.show_menu[index];
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;

    const hasOpenMenu = Object.values(this.show_menu).some(isOpen => isOpen);

    if (hasOpenMenu) {
      const isMenuToggleClick = clickedElement.closest('.actions_w');

      if (!isMenuToggleClick) {
        this.show_menu = {};
      }
    }
  }

  protected readonly Math = Math;

  /**
   * Shows a toaster with the current filter results count
   */
  private showFilterResultsToast() {
    const total = this.audioService.total;
    const message = formatContentCountMessage(total, 'lectures');
    this.toasterService.showToast(message, 'default', 'bottom-middle', 4000);
  }
}
