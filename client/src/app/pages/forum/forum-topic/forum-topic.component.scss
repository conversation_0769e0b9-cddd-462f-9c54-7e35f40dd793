@import url(../forum.component.scss);
@import url(../../lecture/lecture.component.scss);

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -60px;
  left: 50%;
  right: 50%;
  width: 100%;
  transform: translate(-50%, -4%);
}

.spinner {
  width: 80px;
  height: 80px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #464646;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.wrapper_line {
  max-width: 1170px;
  padding: 20px 0 165px 0;
}

.dec_head-title_ {
  padding: 14px 0 42px 0 !important;
}

.forum-category {
  margin-top: 60px;
}

.forum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
  padding: 18px 28px;
  background: var(--selection);
}

.icons_w {
  margin-right: 30px;
}

.fav_hov .on_hov.rem_ {
  left: -40px;
}

.fav_hov .on_hov.rem_::before {
    left: 25px;
}

.topic-user__avatar {
  width: 43px;
  height: 43px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 15px;
  margin-right: 15px;
  color: var(--font-color1);
}

.topic-user {
  display: flex;
}

.topic-user__avatar img {
  position: relative;
  z-index: 1;
  width: 100%;
  border-radius: 50%;
  object-fit: cover;
  height: 100%;
}

.topic-title {
  font-weight: 500;
  font-size: 18px
}

.topic-user_ {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 17px;
  color: var(--font-color1);
}

.topic-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 58px);
}

.topic-images {
  display: flex;
  flex-wrap: wrap;
}

.topic-images div {
  flex-basis: 180px;
  padding: 15px;
}

.topic-images img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.topic-content {
  padding: 30px 40px;

  span {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

.comment-reply {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 25px;
  color: var(--text-color);
  text-decoration: underline;
  cursor: pointer;
}

.topic-comment__footer {
  display: flex;
  padding-left: 40px;
}

.div_sep {
  width: 100%;
  border-top: 1px solid var(--book_about);
  margin: 40px 0;
}

.reply___ {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  color: var(--font-color1);
  border: 1px solid var(--book_about);
  border-bottom: none;
  padding: 30px 30px 0 30px;
}

.editor-toolbar {
  display: flex;
  padding: 25px 30px 15px 30px;
}

// .comment-form textarea {
//   width: 100%;
//   background: #eaeaea;
//   outline: none;
//   padding: 13px;
//   border-radius: 4px;
// }

.reply {

  .forum-header {
    height: fit-content;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .topic-content {
    background: var(--selection);
    padding: 5px 40px 30px 40px;
  }

  .topic-comment__footer {
    background: var(--selection);
    padding-bottom: 40px;
  }
}

.editor-content:empty:before {
  content: attr(placeholder);
  color: var(--font-color1);
}

.editor-content img {
  display: block;
  max-width: 100%;
  margin: 10px 0;
  border-radius: 4px;
}

// Стили для форматированного текста в редакторе
// Используем максимальную специфичность чтобы переопределить глобальные стили

// Основные селекторы
.simple-editor .editor-content {
  strong, b {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  em, i {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }

  // Дополнительные стили для элементов с inline стилями
  [style*="font-weight: bold"], [style*="font-weight:bold"] {
    font-weight: bold !important;
  }

  [style*="font-style: italic"], [style*="font-style:italic"] {
    font-style: italic !important;
  }

  [style*="text-decoration: underline"], [style*="text-decoration:underline"] {
    text-decoration: underline !important;
  }
}

// Дополнительная специфичность для переопределения глобальных стилей
.comment-form .simple-editor .editor-content {
  strong, b {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  em, i {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }
}

// Максимальная специфичность для переопределения любых глобальных стилей
:host .comment-form .simple-editor .editor-content {
  strong, b {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  em, i {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }
}

// Дополнительные селекторы для элементов, созданных execCommand
:host .simple-editor .editor-content {
  strong, b {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  em, i {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }
}

// Глобальное переопределение для редактора (максимальная специфичность)
.editor-content {
  b, strong {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  i, em {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }
}

// Еще более агрессивное переопределение
[contenteditable="true"] {
  b, strong {
    font-weight: bold !important;
    font-family: inherit !important;
  }

  i, em {
    font-style: italic !important;
  }

  u {
    text-decoration: underline !important;
  }
}

.editor-content {
  min-height: 120px;
  outline: none;
  border-top: 1px solid var(--book_about);
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: var(--font-color1);
  padding: 15px 30px 90px 30px;
}

.simple-editor {
  min-height: 275px;
  border-width: 1px;
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
  border: 1px solid var(--book_about);
  border-top: none;
  overflow: hidden;
}

.editor-toolbar button {
  margin-right: 22px;
  cursor: pointer;
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  padding: 6px 8px;
  font-family: Prata;
  font-weight: 400;
  font-size: 16px;
  color: var(--font-color1);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(222, 165, 61, 0.1);
    border-color: var(--book_about);
  }

  &.active-format {
    background-color: var(--book_about) !important;
    color: white !important;
    border-color: var(--book_about) !important;
  }
}

.smile_ {
  position: absolute;
  right: 30px;
  bottom: 11px;
  cursor: pointer;
}

.comment-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: -70px;

  button {
    margin-right: 30px;
  }
}

/////////////////////////////////////////////

.forum-table {
  padding: 15px;
}

.forum-table table {
  background: white;
}

.topic-actions {
  font-size: 12px;
}

.comment-remove {
  color: #f11;
  cursor: pointer;
}

.comment-edit {
  cursor: pointer;
}

.topic-actions a {
  margin-left: 10px;
  cursor: pointer;
}

.cancel-reply {
  margin-left: 10px;
  color: #666;
  cursor: pointer;
  text-decoration: underline;
}

.cancel-reply:hover {
  color: #333;
}



@media (max-width: 768px) {
  .forum-header {
    border-radius: 15px;
    padding: 9px 20px;
  }

  .topic-info {
    width: calc(100% - 50px);
  }

  .icons_w {
    display: flex !important;
  }

  .topic-user__avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .topic-content {
    padding: 20px 25px;

    span {
      font-size: 16px;
      line-height: 21px;
    }
  }

  .topic-comment__footer {
    padding-left: 25px;
  }

  .div_sep {
    margin: 30px 0;
  }

  .reply___ {
    font-size: 17px;
    padding: 25px 20px 0 20px;
  }

  .editor-toolbar {
    padding: 15px 20px;

    button {
      margin-right: 20px;
    }
  }

  .editor-content {
    font-size: 16px;
    line-height: 21px;
    padding: 20px 20px 90px 20px;
  }

  .smile_ {
    right: 20px;
    bottom: 15px;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .comment-btn {
    margin-top: -60px;

    button {
      margin-right: 20px;
    }
  }

  .audio_chip {
    height: 34px;
    font-size: 15px;
  }
}

@media (max-width: 570px) {
  .topic-info {
    flex-direction: column;
    align-items: unset;
  }

  .topic-user_ {
    font-size: 14px;

    &:last-child {
      font-size: 11px;
    }
  }

  .topic-content {
    padding: 15px 10px;

    span {
      font-size: 15px;
      line-height: 20px;
    }
  }

  .icons_w {
    margin-right: 20px;
  }

  .comment-reply {
    font-size: 15px;
    line-height: 20px;
  }

  .div_sep {
    margin: 20px 0 25px 0;
  }

  .topic-comment__footer {
    padding-left: 10px;
  }

  .reply___ {
    font-size: 15px;
    padding: 16px 10px 0 10px;
  }

  .comment-form .editor-toolbar button {
    font-size: 16px;
  }

  .editor-toolbar {
    padding: 15px 10px;
  }

  .smile_ {
    right: 15px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .editor-content {
    font-size: 15px;
    line-height: 18px;
    padding: 15px 10px 80px 10px;
  }

  .audio_chip {
    height: 30px;
    font-size: 13px;
  }

  .comment-btn button {
    margin-right: 15px;
  }

  .comment-btn {
    margin-top: -45px;
  }
}

@media (max-width: 420px) {
  .cat_wrap {
    margin: -60px auto 20px auto !important;
  }
}


.preview-wrapper {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  padding: 20px;
  padding-top: 0px;
}

.preview-card {
  background-color: var(--selection);
  border-radius: 15px;
  padding: 10px;
  width: 182px;
  min-height: 172px;
  position: relative;
  text-align: center;
}

.file-name {
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: var(--font-color1);
  margin-top: 10px;
  word-break: break-all;
}

.preview-card img {
  width: 100%;
  height: 98px;
  object-fit: cover;
  border-radius: 12px;
}


.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--selection);
  border: none;
  color: var(--text-color);
  /* font-size: 18px; */
  width: 32px;
  cursor: pointer;
  border-radius: 50%;
  height: 32px;

  span {
    display: flex;
    width: 100%;
    height: 100%;
    background: var(--x_bt);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 16px;
  }
}

.disable-comments-wrapper {
  margin: 20px 0;
  padding: 15px 20px;
  background: var(--selection);
  border-radius: 10px;
  border: 1px solid var(--book_about);
}

.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: Prata;
  font-size: 16px;
  color: var(--font-color1);
  user-select: none;

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--book_about);
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    background: transparent;

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid var(--font-color1);
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: var(--book_about);
    border-color: var(--book_about);

    &::after {
      opacity: 1;
    }
  }

  .checkbox-label {
    font-weight: 400;
    line-height: 1.4;
  }

  &:hover .checkmark {
    border-color: var(--text-color);
  }
}
