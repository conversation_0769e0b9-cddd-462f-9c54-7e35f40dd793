import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component'
import { environment } from "@/env/environment"
import { FileService } from "@/services/file.service"
import { ForumService } from "@/services/forum.service"
import { ProfileService } from "@/services/profile.service"
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, isPlatformBrowser, JsonPipe, NgOptimizedImage } from "@angular/common"
import { Component, ElementRef, inject, PLATFORM_ID, ViewChild } from '@angular/core'
import { FormArray, FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute, Router } from "@angular/router"
import moment from 'moment'
import 'moment/locale/ru'
moment.locale('ru');

@Component({
  selector: 'app-forum-category',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    JsonPipe
  ],
  templateUrl: './forum-category.component.html',
  styleUrl: './forum-category.component.scss'
})
export class ForumCategoryComponent {
  @ViewChild('topicDialog') topicDialog!: ElementRef<HTMLDialogElement>;
  route = inject(ActivatedRoute);
  editTopicValue: boolean = false;
  router = inject(Router);
  forumService = inject(ForumService);
  toasterService = inject(ToasterService);
  fb = inject(FormBuilder);
  fileService = inject(FileService);
  platformId = inject(PLATFORM_ID);
  titleService = inject(Title)
  metaService = inject(Meta)
  profileService = inject(ProfileService);
  id = this.route.snapshot.paramMap.get('id')!;
  category: any = null
  isLoading: boolean = false;
  isLoadingMore: boolean = false;
  imagePreviews: any[] = [];
  searchQuery: string = "";
  topicForm: any = this.fb.group({
    id: [null],
    category: [null],
    name: [null, [Validators.required, Validators.maxLength(500)]],
    description: [null],
    content: [null, [Validators.required, Validators.minLength(10)]],
    images: this.fb.array([])
  })
  modalHeight: number = 0;
  page: number = 1;
  totalPages: number = 1;

  dropdownSortOpen = false;
  sortOptions = [
    { label: 'Дате', value: 'date', id: 1 },
    { label: 'Последний ответ', value: 'lastReply', id: 1 },
    { label: 'Популярности', value: 'views', id: 2 },
  ];
  sortDirection: 'Asc' | 'Desc' = 'Asc';
  currentSortField: string = 'date';
  selectedSortLabel = 'Дате';
  sort = 'dateDesc'

  ngOnInit() {
    this.getCategory()
  }

  get filteredTopics() {
    if (!this.category || !this.category.topics) {
      return [];
    }

    return this.category.topics.filter((e: any) => e.name.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1)
  }

  get images() {
    return this.topicForm.get('images') as FormArray;
  }

  get isEditable() {
    return (access: any) => access.includes('edit')
  }

  get isRemovable() {
    return (access: any) => access.includes('remove')
  }

  get isModerator() {
    if(!this.profileService?.profile) return false;

    return this.profileService?.profile.groups.some((e: any)=> ['ADMIN', 'FORUM_MODERATOR'].includes(e));
  }

  ngAfterViewInit() {
    //this.showDialog()
  }

  getCategory(loadMore: boolean = false) {
    if (isPlatformBrowser(this.platformId)) {
      if (loadMore) {
        this.isLoadingMore = true;
      } else {
        this.isLoading = true;
        this.page = 1;
      }

      this.forumService.getCategory(+this.id, this.page, this.sort).subscribe({
        next: (res: any) => {
          if (loadMore && this.category) {
            this.category.topics.push(...res.topics);
          } else {
            this.category = res;
            this.topicForm.patchValue({
              category: res.id,
            });
          }
          this.totalPages = res.totalPages;

          this.titleService.setTitle(this.category.name);
          this.metaService.updateTag({ name: 'description', content: this.category.description })
        },
        error: err => {
          this.toasterService.showToast('Ошибка при загрузке данных', 'error', 'bottom-middle');
        },
        complete: () => {
          this.isLoading = false;
          this.isLoadingMore = false;
        }
      });
    }
  }

  showDialog() {
    this.topicDialog.nativeElement.showModal()
    if (isPlatformBrowser(this.platformId)) {
        this.modalHeight = window.innerHeight;
    }
  }

  closeDialog() {
    this.topicDialog.nativeElement.close()
    this.topicForm.reset()
    this.images.reset()
    this.imagePreviews = []
  }

  // uploadFiles(e: Event) {
  //   const files = (e.target as HTMLInputElement).files!;
  //   this.fileService.upload(files, 'forum').subscribe((res: any) => {
  //     for (let image of res) {
  //       (this.topicForm.get('images') as FormArray).push(this.fb.group(image))
  //     }
  //   })
  // }

  uploadFiles(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    if (input.files.length > 5 || (this.imagePreviews.length + input.files.length) > 5) {
      input.value = '';
      return this.toasterService.showToast('Загрузить возможно не более 5 изображений', 'error', 'bottom-middle', 3000);
    }

    const MAX_FILE_SIZE = 10 * 1024 * 1024;
    for (const file of Array.from(input.files)) {
      if (file.size > MAX_FILE_SIZE) {
        this.toasterService.showToast(`Файл "${file.name}" слишком большой. Максимальный размер: 10 МБ.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
      if (!file.type.startsWith('image/')) {
        this.toasterService.showToast(`Файл "${file.name}" не является изображением.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
    }

    this.fileService.upload(input.files, 'forum-images').subscribe({
      next: (response: any) => {
        const uploadedImages = response.map((file: any) => ({
          id: file.id,
          src: this.environment.serverUrl + '/upload/' + file.name,
          name: file.originalName,
          type: 'image/',
        }));
        this.imagePreviews.push(...uploadedImages);
        if (isPlatformBrowser(this.platformId)) {
          this.modalHeight = window.innerHeight;
        }
      },
      error: (err: any) => {
        this.toasterService.showToast('Ошибка при загрузке изображений.', 'error', 'bottom-middle', 3000);
        console.error(err);
      },
      complete: () => {
        input.value = '';
      }
    });
  }

  removeImage(index: number) {
    this.imagePreviews.splice(index, 1);
  }

  removeFile(i: number) {
    this.images.removeAt(i)
  }

  editTopic(topic: any) {
    this.editTopicValue = true;
    while (this.images.length) {
      this.images.removeAt(0);
    }

    this.topicForm.patchValue({
      ...topic,
      category: topic.category.id,
    })

    if (topic.images) {
      for (let image of topic.images) {
        this.images.push(this.fb.group(image))
      }
    }

    this.showDialog();
  }

  deleteTopic(id: number) {
    if (!confirm("Are you sure?")) return
    this.forumService.deleteTopic(id).subscribe({
      next: () => {
        this.toasterService.showToast('Успешно удалена тема!', 'success', 'bottom-middle', 3000);
        if (this.category && this.category.topics) {
          const index = this.category.topics.findIndex((t: any) => t.id === id);
          if (index > -1) {
            this.category.topics.splice(index, 1);
          }
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  onSubmit() {
    this.isLoading = true;

    let finalContent = this.topicForm.get('content')?.value || '';

    if (this.imagePreviews.length > 0) {
      const imagesHtml = this.imagePreviews.map((preview: any) =>
        `<img src="${preview.src}" style="max-width: 100%; margin: 10px 0;">`
      ).join('');

      finalContent += `<br>${imagesHtml}`;
    }

    this.topicForm.patchValue({ content: finalContent });

    this.forumService.createTopic(this.topicForm.value).subscribe({
      next: () => {
        this.toasterService.showToast(this.editTopicValue ? 'Успешно обновлена тема!' : 'Успешно создана тема!', 'success', 'bottom-middle', 3000);
        this.closeDialog();
        this.getCategory()
      },
      error: err => {
        this.toasterService.showToast(err.error.message, 'error', 'bottom-middle');
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
        this.editTopicValue = false;
      }
    })
  }

  getLastComment(comments: any) {
    if (!comments.length) return ''
    const last = comments[comments.length - 1]
    return `${moment(last.createdAt).format('DD.MM.YYYY HH:mm')}`
  }

  nextPage() {
    if (this.page >= this.totalPages || this.isLoadingMore) return;

    this.page++
    this.getCategory(true)
  }

  toggleDropdownSort() {
    this.dropdownSortOpen = !this.dropdownSortOpen;
  }

  selectSort(field: string) {
    if (this.currentSortField === field) {
      this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
    } else {
      this.currentSortField = field;
      this.sortDirection = 'Asc';
    }
    this.sort = this.currentSortField + this.sortDirection;
    this.selectedSortLabel = this.sortOptions.find(option => option.value === field)?.label || '';
    this.getCategory()
  }

  removeTopic(e: Event, id: number) {
    e.preventDefault();
    if(confirm("Удалить тему?")) {
      this.forumService.deleteTopic(id).subscribe(() => this.getCategory())
    }
  }

  protected readonly environment = environment;
}
