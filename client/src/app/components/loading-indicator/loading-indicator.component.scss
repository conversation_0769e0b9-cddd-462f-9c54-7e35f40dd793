.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  animation: fadeInUp 0.5s ease-out;

  p {
    font-family: Prata;
    font-size: 18px;
    color: var(--font-color1);
    margin-top: 20px;
  }

  &.fullscreen-loading {
    min-height: 300px;
  }

  &.small {
    padding: 20px;
    
    p {
      font-size: 14px;
      margin-top: 10px;
    }
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid var(--book_about);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    border: 1px solid var(--book_about);
    opacity: 0.1;
    animation: ripple 3s ease-out infinite;
  }

  &.small-spinner {
    width: 20px;
    height: 20px;
    border-width: 2px;

    &::before {
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-width: 1px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}



@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
