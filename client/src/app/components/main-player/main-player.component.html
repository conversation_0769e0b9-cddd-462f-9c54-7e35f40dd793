@if (tracks.length || emptyPlayer) {
<div>
    <div class="playControls g-z-index-control-bar m-visible">
        <section [ngClass]="{'hidden': (isBackgroundPlay || !tracks.length)}" class="playControls__inner">
            <div class="playControls__wrapper l-container l-fullwidth">
                <!-- <div class="playControls__bg">
                    <div class="album_cover"></div>
                </div> -->
                <div class="playControls__bgg"></div>
                <div class="playControls__elements">
                    <div class="cust_wd pr-2">
                        <div class="flex flex-col">
                            @if(tracks[currentTrackIndex]){
                            <div class="whitespace-nowrap overflow-hidden text-ellipsis pl_title">
                                @if(tracks[currentTrackIndex].title){
                                {{ tracks[currentTrackIndex].title }}
                                } @else {
                                {{tracks[currentTrackIndex].description}}
                                }
                            </div>
                            <div class="whitespace-nowrap overflow-hidden text-ellipsis pl_title auth_sm">
                                @if(tracks[currentTrackIndex]){
                                {{ tracks[currentTrackIndex].authorName || tracks[currentTrackIndex].author }}
                                }
                            </div>
                            <div class="items-center cal_wrp">
                                <div class="flex items-center">
                                    <svg width="22" height="24" viewBox="0 0 22 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                                            fill="var(--text-color)" />
                                        <path
                                            d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                                            fill="var(--text-color)" />
                                    </svg>
                                </div>
                                <span class="ml-2 dt_w">
                                    {{ tracks[currentTrackIndex].date }}
                                </span>
                            </div>
                            }
                        </div>
                    </div>
                    <div class="flex pl_center_wrap">
                        <div #navContner class="flex nav_contner">
                            <button [disabled]="!tracks.length || isRadioPlaying" (click)="backward30Seconds()"
                                class="skipControl playControls__control playControls__prev skipControl__previous skipth"></button>
                            <div class="playControls__repeat playControls__control">
                                <button [disabled]="!tracks.length || (isRadioPlaying && tracks.length == 1)"
                                    [ngClass]="getRepeatClass()" (click)="toggleRepeatState()" title="Repeat"
                                    class="repeatControl">
                                </button>
                            </div>
                            <button [disabled]="!tracks.length || (isRadioPlaying && tracks.length == 1)"
                                (click)="prevTrack()"
                                class="skipControl playControls__control playControls__prev skipControl__previous"></button>
                            <button [disabled]="!tracks.length" [ngClass]="{'paused': isPlaying}" type="button"
                                class="playControl playControls__control" tabindex="" title="Play current"
                                aria-label="Play current" (mousedown)="isPressed = true" (touchstart)="isPressed = true"
                                (mouseup)="isPressed = false;togglePlay();" (touchend)="isPressed = false;togglePlay();"
                                (mouseleave)="isPressed = false">
                                @if (isPressed) {
                                <!-- PRESSED STATE -->
                                @if (!isPlaying) {
                                <span class="dt_ver">
                                    <!-- <svg width="58" height="58" viewBox="0 0 58 58" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <linearGradient id="paint0_linear_0_1" x1="13.5" y1="3" x2="45.5" y2="55.5"
                                                gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop)" />
                                            </linearGradient>
                                            <clipPath id="clip0_0_1">
                                                <rect width="17" height="18" fill="white"
                                                    transform="translate(22 20)" />
                                            </clipPath>
                                        </defs>
                                    </svg> -->
                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g filter="url(#filter0_i_366_619)">
                                            <path
                                                d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
                                                fill="url(#paint0_linear_366_619)" />
                                        </g>
                                        <g filter="url(#filter1_d_366_619)">
                                            <path
                                                d="M19.2796 16.5C19.4026 16.5 19.5256 16.5 19.6609 16.5C20.0298 16.5688 20.3742 16.6948 20.694 16.8667C24.2359 18.7689 27.7902 20.6597 31.3322 22.5619C31.5905 22.6994 31.8365 22.8713 32.0455 23.0661C32.6236 23.5932 32.6482 24.2693 32.1316 24.8537C31.9102 25.1058 31.6274 25.2892 31.3199 25.4496C27.7779 27.3403 24.2359 29.2426 20.6817 31.1333C20.4234 31.2708 20.1405 31.374 19.8577 31.4542C19.0829 31.649 18.431 31.3281 18.1482 30.6291C18.0129 30.3083 17.9883 29.9759 17.9883 29.6322C18.0006 25.8736 18.0006 22.115 18.0006 18.3678C18.0006 18.2189 18.0006 18.0699 18.0252 17.9095C18.0744 17.4969 18.2097 17.1188 18.5417 16.8209C18.7508 16.6375 19.0091 16.5573 19.2796 16.5Z"
                                                fill="white" />
                                        </g>
                                        <defs>
                                            <filter id="filter0_i_366_619" x="-3" y="0" width="51" height="51.8"
                                                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix"
                                                    result="shape" />
                                                <feColorMatrix in="SourceAlpha" type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha" />
                                                <feOffset dx="-3" dy="4" />
                                                <feGaussianBlur stdDeviation="1.9" />
                                                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                                                <feColorMatrix type="matrix"
                                                    values="0 0 0 0 0.326614 0 0 0 0 0.181148 0 0 0 0 0 0 0 0 0.5 0" />
                                                <feBlend mode="normal" in2="shape"
                                                    result="effect1_innerShadow_366_619" />
                                            </filter>
                                            <filter id="filter1_d_366_619" x="13.9883" y="16.5" width="22.5117"
                                                height="23.0104" filterUnits="userSpaceOnUse"
                                                color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feColorMatrix in="SourceAlpha" type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha" />
                                                <feOffset dy="4" />
                                                <feGaussianBlur stdDeviation="2" />
                                                <feComposite in2="hardAlpha" operator="out" />
                                                <feColorMatrix type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                                                <feBlend mode="normal" in2="BackgroundImageFix"
                                                    result="effect1_dropShadow_366_619" />
                                                <feBlend mode="normal" in="SourceGraphic"
                                                    in2="effect1_dropShadow_366_619" result="shape" />
                                            </filter>
                                            <linearGradient id="paint0_linear_366_619" x1="15" y1="3" x2="37" y2="44.25"
                                                gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#B87E2B" />
                                                <stop offset="1" stop-color="#86510E" />
                                            </linearGradient>
                                        </defs>
                                    </svg>

                                </span>
                                <span class="mb_ver">
                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <filter id="filter0_i_366_619" x="-3" y="0" width="51" height="51.8"
                                                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feOffset dx="-3" dy="4" />
                                                <feGaussianBlur stdDeviation="1.9" />
                                                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                                                <feColorMatrix type="matrix"
                                                    values="0 0 0 0 0.326614 0 0 0 0 0.181148 0 0 0 0 0 0 0 0 0.5 0" />
                                                <feBlend mode="normal" in2="shape"
                                                    result="effect1_innerShadow_366_619" />
                                            </filter>
                                            <linearGradient id="paint0_linear_366_619" x1="15" y1="3" x2="37" y2="44.25"
                                                gradientUnits="userSpaceOnUse">
                                                <stop stop-color="var(--pl_start2)" />
                                                <stop offset="1" stop-color="var(--pl_stop2)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                } @else {
                                <span class="dt_ver">
                                    <svg width="58" height="58" viewBox="0 0 58 58" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M34.0991 37.2617C33.015 37.2617 32.0664 36.3131 32.0664 35.229V23.0327C32.0664 21.9486 33.015 21 34.0991 21C35.1832 21 36.1318 21.9486 36.1318 23.0327V35.229C36.1318 36.4486 35.1832 37.2617 34.0991 37.2617Z"
                                            fill="white" />
                                        <defs>
                                            <linearGradient id="paint0_linear_1_94556" x1="13.5" y1="3" x2="45.5"
                                                y2="55.5" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="mb_ver">
                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <linearGradient id="paint0_linear_366_612" x1="11.1724" y1="2.48276"
                                                x2="37.6552" y2="45.931" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start1)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop1)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                }
                                } @else {
                                <!-- NORMAL STATE -->
                                @if (!isPlaying) {
                                <span class="dt_ver">
                                    <svg width="58" height="58" viewBox="0 0 58 58" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M29 58C45.0163 58 58 45.0163 58 29C58 12.9837 45.0163 0 29 0C12.9837 0 0 12.9837 0 29C0 45.0163 12.9837 58 29 58Z"
                                            fill="url(#paint0_linear_0_1)" />
                                        <g clip-path="url(#clip0_0_1)">
                                            <path
                                                d="M23.4984 20C23.6426 20 23.7868 20 23.9454 20C24.3779 20.0825 24.7817 20.2338 25.1566 20.44C29.3092 22.7227 33.4763 24.9916 37.629 27.2743C37.9318 27.4393 38.2202 27.6455 38.4653 27.8793C39.143 28.5118 39.1718 29.3231 38.5662 30.0244C38.3067 30.327 37.975 30.547 37.6146 30.7395C33.4619 33.0084 29.3092 35.2911 25.1421 37.56C24.8393 37.725 24.5077 37.8487 24.1761 37.945C23.2677 38.1788 22.5035 37.7937 22.1718 36.9549C22.0132 36.5699 21.9844 36.1711 21.9844 35.7586C21.9988 31.2483 21.9988 26.738 21.9988 22.2414C21.9988 22.0626 21.9988 21.8839 22.0276 21.6914C22.0853 21.1963 22.2439 20.7426 22.6332 20.385C22.8784 20.165 23.1812 20.0688 23.4984 20Z"
                                                fill="white" />
                                        </g>
                                        <defs>
                                            <linearGradient id="paint0_linear_0_1" x1="13.5" y1="3" x2="45.5" y2="55.5"
                                                gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop)" />
                                            </linearGradient>
                                            <clipPath id="clip0_0_1">
                                                <rect width="17" height="18" fill="white"
                                                    transform="translate(22 20)" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="mb_ver">
                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g filter="url(#filter0_i_366_619)">
                                            <path
                                                d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
                                                fill="url(#paint0_linear_366_619)" />
                                        </g>
                                        <path
                                            d="M19.2796 16.5C19.4026 16.5 19.5256 16.5 19.6609 16.5C20.0298 16.5688 20.3742 16.6948 20.694 16.8667C24.2359 18.7689 27.7902 20.6597 31.3322 22.5619C31.5905 22.6994 31.8365 22.8713 32.0455 23.0661C32.6236 23.5932 32.6482 24.2693 32.1316 24.8537C31.9102 25.1058 31.6274 25.2892 31.3199 25.4496C27.7779 27.3403 24.2359 29.2426 20.6817 31.1333C20.4234 31.2708 20.1405 31.374 19.8577 31.4542C19.0829 31.649 18.431 31.3281 18.1482 30.6291C18.0129 30.3083 17.9883 29.9759 17.9883 29.6322C18.0006 25.8736 18.0006 22.115 18.0006 18.3678C18.0006 18.2189 18.0006 18.0699 18.0252 17.9095C18.0744 17.4969 18.2097 17.1188 18.5417 16.8209C18.7508 16.6375 19.0091 16.5573 19.2796 16.5Z"
                                            fill="white" />
                                        <defs>
                                            <filter id="filter0_i_366_619" x="-3" y="0" width="51" height="51.8"
                                                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix"
                                                    result="shape" />
                                                <feColorMatrix in="SourceAlpha" type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha" />
                                                <feOffset dx="-3" dy="4" />
                                                <feGaussianBlur stdDeviation="1.9" />
                                                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                                                <feColorMatrix type="matrix"
                                                    values="0 0 0 0 0.326614 0 0 0 0 0.181148 0 0 0 0 0 0 0 0 0.5 0" />
                                                <feBlend mode="normal" in2="shape"
                                                    result="effect1_innerShadow_366_619" />
                                            </filter>
                                            <linearGradient id="paint0_linear_366_619" x1="15" y1="3" x2="37" y2="44.25"
                                                gradientUnits="userSpaceOnUse">
                                                <stop stop-color="var(--pl_start2)" />
                                                <stop offset="1" stop-color="var(--pl_stop2)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                } @else {
                                <span class="dt_ver">
                                    <svg width="58" height="58" viewBox="0 0 58 58" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M29 58C45.0163 58 58 45.0163 58 29C58 12.9837 45.0163 0 29 0C12.9837 0 0 12.9837 0 29C0 45.0163 12.9837 58 29 58Z"
                                            fill="url(#paint0_linear_1_94556)" />
                                        <path
                                            d="M24.0327 37.2617C22.9486 37.2617 22 36.3131 22 35.229V23.0327C22 21.9486 22.9486 21 24.0327 21C25.1168 21 26.0654 21.9486 26.0654 23.0327V35.229C26.0654 36.4486 25.1168 37.2617 24.0327 37.2617Z"
                                            fill="white" />
                                        <path
                                            d="M34.0991 37.2617C33.015 37.2617 32.0664 36.3131 32.0664 35.229V23.0327C32.0664 21.9486 33.015 21 34.0991 21C35.1832 21 36.1318 21.9486 36.1318 23.0327V35.229C36.1318 36.4486 35.1832 37.2617 34.0991 37.2617Z"
                                            fill="white" />
                                        <defs>
                                            <linearGradient id="paint0_linear_1_94556" x1="13.5" y1="3" x2="45.5"
                                                y2="55.5" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="mb_ver">
                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
                                            fill="url(#paint0_linear_366_612)" />
                                        <path
                                            d="M19.375 31.5C18.375 31.5 17.5 30.625 17.5 29.625V18.375C17.5 17.375 18.375 16.5 19.375 16.5C20.375 16.5 21.25 17.375 21.25 18.375V29.625C21.25 30.75 20.375 31.5 19.375 31.5Z"
                                            fill="white" />
                                        <path
                                            d="M28.6602 31.5C27.6602 31.5 26.7852 30.625 26.7852 29.625V18.375C26.7852 17.375 27.6602 16.5 28.6602 16.5C29.6602 16.5 30.5352 17.375 30.5352 18.375V29.625C30.5352 30.75 29.6602 31.5 28.6602 31.5Z"
                                            fill="white" />
                                        <defs>
                                            <linearGradient id="paint0_linear_366_612" x1="11.1724" y1="2.48276"
                                                x2="37.6552" y2="45.931" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.157692" stop-color="var(--pl_start1)" />
                                                <stop offset="0.85344" stop-color="var(--pl_stop1)" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                }
                                }
                            </button>
                            <button [disabled]="!tracks.length || (isRadioPlaying && tracks.length == 1)"
                                (click)="nextTrack()"
                                class="skipControl playControls__control playControls__next skipControl__next"
                                tabindex=""></button>
                            <div class="playControls__shuffle playControls__control">
                                <button [disabled]="!tracks.length || (isRadioPlaying && tracks.length == 1)"
                                    (click)="shuffle()" [ngClass]="{'m-shuffling': isShuffle}"
                                    class="shuffleControl"></button>
                            </div>
                            <button [disabled]="!tracks.length || isRadioPlaying" (click)="forward30Seconds()"
                                class="skipControl playControls__control playControls__next skipControl__next skipth"
                                tabindex=""></button>
                        </div>
                        <div class="playControls__timeline">
                            <div class="playbackTimeline is-scrubbable has-sound" (click)="seekTo($event)">
                                <div class="playbackTimeline__timePassed">
                                    <span aria-hidden="true">{{ emptyPlayer ? '0:00' : formatTime(currentTime) }}</span>
                                </div>
                                <div class="playbackTimeline__progressWrapper sc-mx-1x" role="progressbar"
                                    [attr.aria-valuemax]="duration" [attr.aria-valuenow]="currentTime">
                                    <div class="playbackTimeline__progressBackground"></div>
                                    <div class="playbackTimeline__progressBar" [style.width]="progressBarWidth"></div>
                                    <div class="playbackTimeline__progressHandle" [style.left.%]="progressPercentage"
                                        draggable="true" (drag)="seekTo($event)">
                                    </div>
                                </div>
                                <div class="playbackTimeline__duration">
                                    <span aria-hidden="true">{{ emptyPlayer ? '0:00' : formatTime(duration) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center nav_playr">
                        <!-- <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    <button (click)="toggleMute()" [ngClass]="{'muted': (audio ? audio.muted : 0)}"
                                        type="button" class="volume__button">
                                    </button>
                                </div>
                                <div class="volume__sliderWrapper" role="slider" aria-valuemin="0" aria-valuemax="1"
                                    aria-label="Volume">
                                    <input class="range_i" type="range" min="0" max="1" step="0.01"
                                        [value]="(audio ? audio.volume : 0)"
                                        (input)="changeVolume($event); updateVolumeProgress()" />
                                    <div class="volume__sliderBackground"></div>
                                    <div class="volume__sliderProgress"></div>
                                    <div class="volume__sliderProgress_bbc">
                                        <div class="relative h-[100%]">
                                            <div class="volume__sliderHandle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="playControls__volume">
                            <div class="volume hover spec_onee">
                                <div class="volume__iconWrapper">
                                    <button [disabled]="isRadioPlaying" type="button" class="volume__button speed">
                                        {{ currentSpeed }}X
                                    </button>
                                </div>
                                <div [ngStyle]="{'display': isRadioPlaying ? 'none' : ''}"
                                    class="volume__sliderWrapper vol_m">
                                    <ul class="speed-options">
                                        <li class="spd" *ngFor="let speed of speedOptions"
                                            [class.selected]="speed === currentSpeed" (click)="setPlaybackSpeed(speed)">
                                            {{ speed }}x
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    @if (!emptyPlayer && tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
                                    <button [disabled]="isRadioPlaying"
                                        [ngClass]="{'in-favourites': tracks[currentTrackIndex]!.id | isInPlaylist : playlists}"
                                        type="button" (click)="showPlaylistDialog()">
                                        <svg width="27" height="26" viewBox="0 0 27 26" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.96868 24.9618C6.94253 24.9618 6.91638 24.9618 6.89022 24.9618C6.36715 24.9356 5.89638 24.6999 5.53023 24.3333C5.16408 23.9143 4.98101 23.3906 5.03332 22.8145C5.05947 22.5789 5.11178 22.317 5.19024 22.0552C5.71331 20.4055 6.41946 18.2844 7.1256 16.1372C5.42562 14.9065 3.72564 13.6758 2.02565 12.4189C1.81642 12.2618 1.16258 11.7904 1.03182 10.9525C0.927201 10.3502 1.08412 9.77414 1.45027 9.35517C1.84258 8.88383 2.44411 8.62197 3.15026 8.62197C3.90871 8.62197 4.66717 8.62197 5.42562 8.62197H9.55789C9.68866 8.25537 9.79327 7.88878 9.92404 7.52218C10.081 7.05084 10.2117 6.60568 10.3686 6.13435C10.7348 5.00837 11.0748 3.8562 11.4671 2.73022C11.5979 2.31125 11.8333 1.91847 12.1209 1.60424C12.4871 1.21146 13.0363 0.975792 13.5855 1.00198C14.1348 1.02816 14.6578 1.29002 15.024 1.73517C15.2332 2.02321 15.4163 2.33744 15.5471 2.70404C16.044 4.24899 16.5409 5.79393 17.0378 7.33888L17.4563 8.62197H18.6593C20.3332 8.62197 22.007 8.62197 23.6808 8.62197C24.047 8.62197 24.387 8.67434 24.7008 8.77909C25.2762 8.98857 25.7208 9.43372 25.9039 10.0098C26.087 10.5859 26.0085 11.1882 25.6685 11.7119C25.4854 11.9999 25.25 12.2356 24.9624 12.4451C23.2624 13.6758 21.5624 14.9327 19.8886 16.1372C20.5424 18.1273 21.1962 20.0912 21.8239 22.0813C22.0593 22.7884 22.0332 23.3906 21.7455 23.9405C21.327 24.7785 20.3593 25.1974 19.4701 24.9094C19.1301 24.8046 18.8163 24.6475 18.5286 24.438C17.1424 23.443 15.7563 22.4218 14.344 21.4005L13.5071 20.7983L12.5132 21.5314C11.1794 22.5003 9.84558 23.4954 8.51174 24.4642C7.96252 24.7785 7.4656 24.9618 6.96868 24.9618ZM4.45794 10.1407C4.01333 10.1407 3.59487 10.1407 3.15026 10.1407C2.91487 10.1407 2.7318 10.2193 2.65334 10.324C2.57488 10.4026 2.54872 10.5335 2.57488 10.6644C2.60103 10.7692 2.65334 10.9001 2.94103 11.1096C4.71947 12.3927 6.47176 13.702 8.25021 14.9851C8.69482 15.3255 8.85174 15.7968 8.69482 16.3205C7.96252 18.5463 7.23022 20.7983 6.68099 22.5003C6.62869 22.6312 6.60253 22.7884 6.60253 22.8931C6.60253 23.0502 6.62869 23.155 6.70715 23.2597C6.75945 23.3383 6.86407 23.3644 6.94253 23.3644C7.0733 23.3644 7.23022 23.3383 7.54406 23.1026C8.87789 22.1337 10.2117 21.1387 11.5456 20.1698L13.4809 18.7558L15.2594 20.0389C16.6455 21.0601 18.0317 22.0552 19.4178 23.0764C19.5747 23.1811 19.7316 23.2597 19.9147 23.3121C20.0716 23.3644 20.2286 23.2859 20.307 23.1288C20.3593 23.024 20.4116 22.8407 20.2809 22.4741C19.6009 20.4055 18.9209 18.3368 18.2409 16.2682C18.0578 15.7183 18.2147 15.2731 18.6593 14.9327C20.4116 13.6758 22.1901 12.3665 23.9424 11.0834C24.0731 10.9787 24.1777 10.8739 24.2824 10.743C24.3608 10.6383 24.387 10.5073 24.3347 10.4026C24.3085 10.2978 24.2039 10.2193 24.0993 10.1669C23.9685 10.1145 23.7854 10.0884 23.6024 10.0884C21.9285 10.0884 20.2547 10.0884 18.5809 10.0884H16.2794L16.0963 9.59084C16.0701 9.51228 16.044 9.45991 16.0178 9.40754L15.4948 7.78404C14.9978 6.23909 14.5009 4.69414 14.004 3.14919C13.9517 2.96589 13.8471 2.78259 13.7425 2.65167C13.664 2.54692 13.5332 2.52074 13.4809 2.52074C13.4286 2.52074 13.324 2.52074 13.2455 2.62548C13.1148 2.75641 13.0102 2.96589 12.9317 3.17538C12.5656 4.30136 12.1994 5.45352 11.8333 6.55331C11.6763 7.02465 11.5456 7.46981 11.3886 7.94115C11.2579 8.30775 11.1533 8.70053 11.0225 9.06713L10.6825 10.1407H5.42562C5.11178 10.1407 4.77178 10.1407 4.45794 10.1407Z"
                                                fill="white" stroke="white" stroke-width="0.4" />
                                        </svg>
                                    </button>
                                    <button [ngClass]="{'is-liked': tracks[currentTrackIndex].liked}"
                                        (click)="like(tracks[currentTrackIndex]); $event.stopPropagation();">
                                        <div class="flex">
                                            @if (tracks[currentTrackIndex].liked) {
                                            <svg width="24" height="22" viewBox="0 0 24 22" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.623 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249Z"
                                                    fill="white" />
                                            </svg>
                                            } @else {
                                            <svg width="24" height="22" viewBox="0 0 24 22" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                                    fill="white" />
                                            </svg>
                                            }
                                            <!-- <span class="ml-1">{{tracks[currentTrackIndex].likes}}</span> -->
                                        </div>
                                    </button>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    <button [ngClass]="{'pressed': showList}" type="button" class="list__button"
                                        (click)="showList = !showList">
                                    </button>
                                </div>
                                <div [ngClass]="{'show_list': showList}" class="volume__sliderWrapper list">
                                    @if(tracks.length){
                                    <div class="flex bs_wrapper_">
                                        <p (click)="clear()" class="btn_item_wrapper">
                                            <span class="btn_item_">Очистить</span>
                                        </p>
                                    </div>
                                    }
                                    <p (click)="showList = false" class="absolute close_list cursor-pointer"></p>
                                    <div class="m-auto w-fit pt-4" *ngIf="!tracks.length">
                                        Нет добавленных аудио.
                                    </div>
                                    <div class="absolute que_lst" *ngIf="tracks.length">
                                        Очередь воспроизведения
                                    </div>
                                    <!-- <div class="absolute pl-4 text-lg top-[35px]" *ngIf="tracks.length">
                                        {{currentTrackIndex + 1}} из {{tracks.length}}
                                    </div> -->
                                    <ul class="ul-list" *ngIf="tracks.length">
                                        <li class="play_list py-1 flex items-center"
                                            [ngClass]="{ 'current': currentTrackIndex == i, 'borders_': last }"
                                            *ngFor="let track of tracks; let i = index; last as last"
                                            (click)="selectTrack(i)" (dragover)="onDragOver($event)"
                                            (drop)="onDrop($event, i)">
                                            <span title="Drag to move Click to Open" draggable="true"
                                                (dragstart)="onDragStart(i, $event)" class="cursor-move list__"></span>
                                            <span>
                                                <!-- {{ i + 1 }}.  -->
                                                @if(track){
                                                {{ track.authorName }}
                                                }
                                            </span>
                                            <span
                                                class="block w-[57%] l_item_nm whitespace-nowrap overflow-hidden text-ellipsis">
                                                @if(track){
                                                @if(track.title){
                                                {{ track.title }}
                                                } @else {
                                                {{track.description}}
                                                }
                                                }
                                            </span>
                                            <span class="show_dots spect_ track-remove" (click)="removeTrack(i); $event.stopPropagation();">
                                                <svg class="emty_f" width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z"
                                                        fill="var(--st_back)" />
                                                </svg>
                                            </span>
                                            <span [ngClass]="{'is-liked': track.liked}"
                                                (click)="like(track); $event.stopPropagation();" class="spect_">
                                                @if (track.liked) {
                                                <span>
                                                    <svg width="22" height="20" viewBox="0 0 24 22" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.623 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249Z"
                                                            fill="var(--text-color)" />
                                                    </svg>
                                                </span>
                                                } @else {
                                                <svg width="22" height="20" viewBox="0 0 22 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M10.7739 2.13863C10.6113 1.9992 10.4613 1.86611 10.3112 1.72668C9.21062 0.68098 8.16635 0.148622 7.03453 0.0535581C6.82818 0.0345453 6.62182 0.0282077 6.42172 0.0282077C4.47075 0.0282077 2.85118 0.826744 1.5818 2.41748C0.262383 4.07159 -0.231615 6.04892 0.0998015 8.29877C0.368686 10.1177 1.15033 11.6197 2.41972 12.7604C4.35194 14.4906 6.34043 16.2208 8.2664 17.8875C8.97926 18.5086 9.69211 19.1234 10.405 19.7445C10.5113 19.8395 10.6238 19.9092 10.7301 19.9473C11.0365 20.0613 11.3179 19.9916 11.6118 19.7255C12.7061 18.7685 13.8004 17.8115 14.901 16.8545C15.6514 16.1954 16.408 15.5426 17.1584 14.8835C17.421 14.6554 17.6899 14.4336 17.9463 14.2117C18.5403 13.7111 19.1531 13.1977 19.7034 12.6337C21.6544 10.6183 22.3672 8.21004 21.8232 5.48488C21.3605 3.17799 20.1224 1.54289 18.1464 0.617604C16.2517 -0.269659 14.457 -0.199946 12.8249 0.820407C12.2121 1.20066 11.6431 1.70767 11.1303 2.32242L11.0678 2.39847L10.999 2.33509C10.924 2.26538 10.8489 2.202 10.7739 2.13863ZM11.6931 4.07159C11.7494 3.9892 11.8057 3.90682 11.862 3.82443C12.0621 3.52656 12.2684 3.22236 12.5185 2.96251C13.8692 1.53656 15.989 1.26404 17.7899 2.29707C19.1218 3.05758 19.941 4.26806 20.2349 5.89048C20.6539 8.24173 19.9535 10.2444 18.1401 11.8351C16.5205 13.2548 14.8697 14.6997 13.2689 16.0877C12.5873 16.6834 11.9057 17.2728 11.2304 17.8685C11.1991 17.9002 11.1616 17.9256 11.1241 17.9573L10.999 18.0587L7.34719 14.8709C6.95949 14.5286 6.55929 14.1864 6.1716 13.8568C5.31492 13.1217 4.42697 12.3612 3.5828 11.5753C2.36344 10.4409 1.66934 8.88816 1.63807 7.2087C1.60681 5.61797 2.17584 4.10328 3.20761 3.05758C4.60206 1.64429 6.81567 1.27038 8.4665 2.16398C9.11683 2.51888 9.6671 3.05124 10.1361 3.79274C10.1611 3.83076 10.1861 3.86879 10.2049 3.90682C10.2361 3.96385 10.2674 4.01455 10.3049 4.06526C10.4675 4.30608 10.7176 4.45185 10.9865 4.45185H10.9928C11.2679 4.45819 11.5243 4.31876 11.6931 4.07159Z"
                                                        fill="var(--book_about)" />
                                                </svg>
                                                }
                                            </span>
                                            <span class="relative tm_rt show_time right-[0] top-[2px]">{{ track.time ?
                                                track.time :
                                                convertSecondsToTime(track.duration)}}
                                            </span>
                                            <span class="relative dots hvr tm_rt show_dots right-[0] top-[2px]">
                                                <svg width="24" height="5" viewBox="0 0 24 5" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="21.3637" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                    <circle cx="11.7499" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                    <circle cx="2.13636" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                </svg>
                                                <span class="on_hover py-2">
                                                    <div class="addtnl_sp"></div>
                                                    <!-- <p (click)="remove(i); $event.stopPropagation();"
                                                        class="dropdown-item cat_i">
                                                        <span>
                                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M4.77495 19.9681C4.75402 19.9681 4.7331 19.9681 4.71218 19.9681C4.29372 19.9463 3.91711 19.7499 3.62419 19.4444C3.33127 19.0953 3.18481 18.6589 3.22665 18.1788C3.24758 17.9824 3.28942 17.7642 3.35219 17.546C3.77065 16.1712 4.33557 14.4037 4.90048 12.6144C3.5405 11.5888 2.18051 10.5632 0.820522 9.51573C0.653139 9.3848 0.130067 8.99202 0.0254527 8.29374C-0.0582388 7.79185 0.0672984 7.31178 0.360219 6.96264C0.674062 6.56986 1.15529 6.35164 1.72021 6.35164C2.32697 6.35164 2.93373 6.35164 3.5405 6.35164H6.84631C6.95092 6.04615 7.03462 5.74065 7.13923 5.43515C7.26477 5.04237 7.36938 4.6714 7.49492 4.27862C7.78784 3.34031 8.05984 2.38017 8.37368 1.44185C8.47829 1.09271 8.6666 0.765393 8.89675 0.503537C9.18967 0.176218 9.62905 -0.0201736 10.0684 0.00164767C10.5078 0.023469 10.9263 0.241682 11.2192 0.612644C11.3866 0.852678 11.533 1.11453 11.6376 1.42003C12.0352 2.70749 12.4327 3.99494 12.8303 5.2824L13.165 6.35164H14.1275C15.4665 6.35164 16.8056 6.35164 18.1447 6.35164C18.4376 6.35164 18.7096 6.39529 18.9607 6.48257C19.421 6.65714 19.7766 7.0281 19.9231 7.50817C20.0696 7.98824 20.0068 8.49013 19.7348 8.92656C19.5883 9.16659 19.4 9.36298 19.1699 9.53755C17.8099 10.5632 16.4499 11.6106 15.1108 12.6144C15.6339 14.2728 16.157 15.9094 16.6591 17.5678C16.8474 18.157 16.8265 18.6589 16.5964 19.1171C16.2616 19.8154 15.4875 20.1645 14.7761 19.9245C14.5041 19.8372 14.253 19.7063 14.0229 19.5317C12.9139 18.7025 11.805 17.8515 10.6752 17.0004L10.0057 16.4985L9.2106 17.1095C8.14353 17.9169 7.07646 18.7461 6.0094 19.5535C5.57002 19.8154 5.17248 19.9681 4.77495 19.9681ZM2.76635 7.61728C2.41066 7.61728 2.07589 7.61728 1.72021 7.61728C1.5319 7.61728 1.38544 7.68274 1.32267 7.77003C1.2599 7.83549 1.23898 7.9446 1.2599 8.05371C1.28083 8.14099 1.32267 8.2501 1.55282 8.42467C2.97558 9.49391 4.37741 10.585 5.80017 11.6542C6.15586 11.9379 6.28139 12.3307 6.15586 12.7671C5.57001 14.6219 4.98417 16.4985 4.54479 17.9169C4.50295 18.026 4.48203 18.157 4.48203 18.2442C4.48203 18.3752 4.50295 18.4625 4.56572 18.5497C4.60756 18.6152 4.69125 18.637 4.75402 18.637C4.85864 18.637 4.98417 18.6152 5.23525 18.4188C6.30232 17.6114 7.36938 16.7822 8.43645 15.9748L9.98474 14.7965L11.4075 15.8657C12.5164 16.7168 13.6253 17.546 14.7342 18.397C14.8598 18.4843 14.9853 18.5497 15.1318 18.5934C15.2573 18.637 15.3828 18.5716 15.4456 18.4406C15.4875 18.3534 15.5293 18.2006 15.4247 17.8951C14.8807 16.1712 14.3367 14.4473 13.7927 12.7235C13.6462 12.2652 13.7718 11.8943 14.1275 11.6106C15.5293 10.5632 16.9521 9.47209 18.3539 8.40285C18.4585 8.31556 18.5422 8.22828 18.6259 8.11917C18.6887 8.03188 18.7096 7.92278 18.6677 7.83549C18.6468 7.74821 18.5631 7.68274 18.4794 7.6391C18.3748 7.59546 18.2284 7.57364 18.0819 7.57364C16.7428 7.57364 15.4038 7.57364 14.0647 7.57364H12.2235L12.077 7.15903C12.0561 7.09357 12.0352 7.04993 12.0143 7.00628L11.5958 5.65336C11.1983 4.36591 10.8007 3.07845 10.4032 1.79099C10.3614 1.63824 10.2777 1.4855 10.194 1.37639C10.1312 1.2891 10.0266 1.26728 9.98474 1.26728C9.9429 1.26728 9.8592 1.26728 9.79644 1.35457C9.69182 1.46367 9.60813 1.63824 9.54536 1.81281C9.25244 2.75113 8.95952 3.71127 8.6666 4.62776C8.54106 5.02055 8.43645 5.39151 8.31091 5.78429C8.2063 6.08979 8.12261 6.41711 8.01799 6.72261L7.74599 7.61728H3.5405C3.28942 7.61728 3.01742 7.61728 2.76635 7.61728Z"
                                                                    fill="var(--font-color1)" />
                                                            </svg>
                                                        </span>
                                                        Удалить
                                                    </p> -->
                                                    <p (click)="share(track); $event.stopPropagation();"
                                                        class="dropdown-item cat_i">
                                                        <span>
                                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M13.2698 5.84174C11.2555 6.84951 9.24711 7.85131 7.22087 8.86503C7.44733 9.61042 7.45329 10.3558 7.22087 11.1131C9.23519 12.1209 11.2436 13.1287 13.2281 14.1185C13.538 13.8323 13.8121 13.5222 14.1339 13.2897C16.3628 11.6617 19.5452 13.0094 19.9445 15.7465C20.2901 18.1257 18.3473 20.189 15.9456 19.9862C14.2412 19.8431 12.8109 18.4597 12.6262 16.7542C12.5904 16.4024 12.5964 16.0386 12.644 15.6928C12.6738 15.49 12.6202 15.4244 12.4593 15.3469C10.731 14.4882 9.00873 13.6236 7.28046 12.7589C7.0838 12.6576 6.88118 12.5681 6.68451 12.4548C6.56532 12.3892 6.49977 12.4071 6.40441 12.5085C5.63563 13.3195 4.69403 13.713 3.57364 13.7011C1.84537 13.6773 0.272056 12.2879 0.0396338 10.5705C-0.240464 8.54899 0.987199 6.77199 2.9896 6.36649C4.3007 6.10412 5.43897 6.49768 6.38058 7.45178C6.50573 7.577 6.5832 7.58893 6.73219 7.51141C8.6452 6.54539 10.5642 5.59129 12.4831 4.6372C12.6321 4.56564 12.6798 4.50004 12.644 4.32115C12.2746 2.29966 13.7048 0.325875 15.7311 0.0396458C17.8527 -0.264472 19.7597 1.21438 19.9802 3.33128C20.1888 5.31103 18.7228 7.1417 16.7442 7.36233C15.3497 7.52334 14.2293 7.01647 13.3532 5.93715C13.3234 5.9133 13.2996 5.88348 13.2698 5.84174ZM5.95149 9.99802C5.95149 8.75173 4.92645 7.72012 3.68687 7.72608C2.45324 7.73205 1.43416 8.74577 1.42225 9.98013C1.41033 11.2145 2.43537 12.2521 3.68091 12.264C4.92049 12.2759 5.95149 11.2503 5.95149 9.99802ZM14.0267 16.2951C14.0207 17.5413 15.0457 18.573 16.2853 18.567C17.5189 18.567 18.5559 17.5294 18.5559 16.2951C18.5499 15.0607 17.5428 14.0529 16.2972 14.041C15.0517 14.0231 14.0326 15.0368 14.0267 16.2951ZM16.2853 5.95504C17.5309 5.95504 18.5559 4.92939 18.5559 3.68906C18.5499 2.44874 17.513 1.41712 16.2794 1.42308C15.0398 1.42905 14.0386 2.44278 14.0267 3.6831C14.0207 4.93535 15.0338 5.95504 16.2853 5.95504Z"
                                                                    fill="var(--font-color1)" />
                                                            </svg>
                                                        </span>
                                                        Поделиться
                                                    </p>
                                                    <div title="Добавить в Избранное1" class="dropdown-item cat_i"
                                                        [ngClass]="{'in-favourites': track.id | isInPlaylist : playlists}"
                                                        (click)="showPlaylistDialog(); $event.stopPropagation();">
                                                        <div class="icons_w icon-wrap star_w">
                                                            <app-favorites-icon></app-favorites-icon>
                                                        </div>
                                                        <span class="ml-[8px]">Избранное</span>
                                                    </div>
                                                </span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <button (click)="closePlayer()" class="cl_bun"><span>Закрыть</span></button>
                    </div>
                    <div class="flex items-center nav_playr mobile_v">
                        <!-- <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    <button (click)="toggleMute()" [ngClass]="{'muted': (audio ? audio.muted : 0)}"
                                        type="button" class="volume__button">
                                    </button>
                                </div>
                                <div class="volume__sliderWrapper" role="slider" aria-valuemin="0" aria-valuemax="1"
                                    aria-label="Volume">
                                    <input class="range_i" type="range" min="0" max="1" step="0.01"
                                        [value]="(audio ? audio.volume : 0)"
                                        (input)="changeVolume($event); updateVolumeProgress()" />
                                    <div class="volume__sliderBackground"></div>
                                    <div class="volume__sliderProgress"></div>
                                    <div class="volume__sliderProgress_bbc">
                                        <div class="relative h-[100%]">
                                            <div class="volume__sliderHandle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    @if (!emptyPlayer && tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
                                    <button class="mob_share"
                                        (click)="share(tracks[currentTrackIndex]!); $event.stopPropagation();">
                                    </button>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    @if (!emptyPlayer && tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
                                    <button class="mob_lst" (click)="showPlaylistDialog(); $event.stopPropagation();">
                                    </button>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    @if (!emptyPlayer && tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
                                    <button class="mob_like" [ngClass]="{'is-liked': tracks[currentTrackIndex]!.liked}"
                                        (click)="like(tracks[currentTrackIndex]); $event.stopPropagation();">
                                    </button>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    @if (!emptyPlayer && tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
                                    <button [disabled]="isRadioPlaying" class="mob_star"
                                        [ngClass]="{'in-favourites': tracks[currentTrackIndex]!.id | isInPlaylist : playlists}"
                                        type="button" (click)="showPlaylistDialog()">
                                    </button>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover spec_onee">
                                <div class="volume__iconWrapper">
                                    <button [disabled]="isRadioPlaying" type="button" class="volume__button speed">
                                        {{ currentSpeed }}X
                                    </button>
                                </div>
                                <div [ngStyle]="{'display': isRadioPlaying ? 'none' : ''}"
                                    class="volume__sliderWrapper vol_m">
                                    <ul class="speed-options">
                                        <li class="spd" *ngFor="let speed of speedOptions"
                                            [class.selected]="speed === currentSpeed" (click)="setPlaybackSpeed(speed)">
                                            {{ speed }}x
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="playControls__volume">
                            <div class="volume hover">
                                <div class="volume__iconWrapper">
                                    <button [ngClass]="{'pressed': showList}" type="button" class="list__button"
                                        (click)="showList = !showList">
                                    </button>
                                </div>
                                <div [ngClass]="{'show_list': showList}" class="spinn_"></div>
                                <div [ngClass]="{'show_list': showList}" class="volume__sliderWrapper list">
                                    @if(tracks.length){
                                    <div class="flex bs_wrapper_">
                                        <p (click)="clear()" class="btn_item_wrapper">
                                            <span class="btn_item_">Очистить</span>
                                        </p>
                                    </div>
                                    }
                                    <p (click)="showList = false" class="absolute close_list cursor-pointer"></p>
                                    <div class="m-auto w-fit pt-4" *ngIf="!tracks.length">
                                        Нет добавленных аудио.
                                    </div>
                                    <div class="absolute que_lst" *ngIf="tracks.length">
                                        Очередь воспроизведения
                                    </div>
                                    <!-- <div class="absolute pl-4 text-lg top-[35px]" *ngIf="tracks.length">
                                        {{currentTrackIndex + 1}} из {{tracks.length}}
                                    </div> -->
                                    <ul class="ul-list" *ngIf="tracks.length">
                                        <li class="play_list py-1 flex items-center"
                                            [ngClass]="{ 'current': currentTrackIndex == i }"
                                            *ngFor="let track of tracks; let i = index" (click)="selectTrack(i)"
                                            (dragover)="onDragOver($event)" (drop)="onDrop($event, i)">
                                            <span title="Drag to move Click to Open" draggable="true"
                                                (dragstart)="onDragStart(i, $event)" class="cursor-move list__"></span>
                                            <span>
                                                <!-- {{ i + 1 }}.  -->
                                                @if(track){
                                                {{ track.authorName }}
                                                }
                                            </span>
                                            <span
                                                class="block w-[57%] l_item_nm whitespace-nowrap overflow-hidden text-ellipsis">
                                                @if(track){
                                                @if(track.title){
                                                {{ track.title }}
                                                } @else {
                                                {{track.description}}
                                                }
                                                }
                                            </span>
                                            <span class="show_dots spect_ track-remove" (click)="removeTrack(i); $event.stopPropagation();">
                                                <svg class="emty_f" width="16" height="20" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z"
                                                        fill="var(--st_back)" />
                                                    <path
                                                        d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z"
                                                        fill="var(--st_back)" />
                                                </svg>
                                            </span>
                                            <span [ngClass]="{'is-liked': track.liked}"
                                                (click)="like(track.id); $event.stopPropagation();" class="spect_">
                                                @if (track.liked) {
                                                <span>
                                                    <svg width="18" height="16" viewBox="0 0 24 22" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.623 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249Z"
                                                            fill="var(--text-color)" />
                                                    </svg>
                                                </span>
                                                } @else {
                                                <svg width="18" height="16" viewBox="0 0 22 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M10.7739 2.13863C10.6113 1.9992 10.4613 1.86611 10.3112 1.72668C9.21062 0.68098 8.16635 0.148622 7.03453 0.0535581C6.82818 0.0345453 6.62182 0.0282077 6.42172 0.0282077C4.47075 0.0282077 2.85118 0.826744 1.5818 2.41748C0.262383 4.07159 -0.231615 6.04892 0.0998015 8.29877C0.368686 10.1177 1.15033 11.6197 2.41972 12.7604C4.35194 14.4906 6.34043 16.2208 8.2664 17.8875C8.97926 18.5086 9.69211 19.1234 10.405 19.7445C10.5113 19.8395 10.6238 19.9092 10.7301 19.9473C11.0365 20.0613 11.3179 19.9916 11.6118 19.7255C12.7061 18.7685 13.8004 17.8115 14.901 16.8545C15.6514 16.1954 16.408 15.5426 17.1584 14.8835C17.421 14.6554 17.6899 14.4336 17.9463 14.2117C18.5403 13.7111 19.1531 13.1977 19.7034 12.6337C21.6544 10.6183 22.3672 8.21004 21.8232 5.48488C21.3605 3.17799 20.1224 1.54289 18.1464 0.617604C16.2517 -0.269659 14.457 -0.199946 12.8249 0.820407C12.2121 1.20066 11.6431 1.70767 11.1303 2.32242L11.0678 2.39847L10.999 2.33509C10.924 2.26538 10.8489 2.202 10.7739 2.13863ZM11.6931 4.07159C11.7494 3.9892 11.8057 3.90682 11.862 3.82443C12.0621 3.52656 12.2684 3.22236 12.5185 2.96251C13.8692 1.53656 15.989 1.26404 17.7899 2.29707C19.1218 3.05758 19.941 4.26806 20.2349 5.89048C20.6539 8.24173 19.9535 10.2444 18.1401 11.8351C16.5205 13.2548 14.8697 14.6997 13.2689 16.0877C12.5873 16.6834 11.9057 17.2728 11.2304 17.8685C11.1991 17.9002 11.1616 17.9256 11.1241 17.9573L10.999 18.0587L7.34719 14.8709C6.95949 14.5286 6.55929 14.1864 6.1716 13.8568C5.31492 13.1217 4.42697 12.3612 3.5828 11.5753C2.36344 10.4409 1.66934 8.88816 1.63807 7.2087C1.60681 5.61797 2.17584 4.10328 3.20761 3.05758C4.60206 1.64429 6.81567 1.27038 8.4665 2.16398C9.11683 2.51888 9.6671 3.05124 10.1361 3.79274C10.1611 3.83076 10.1861 3.86879 10.2049 3.90682C10.2361 3.96385 10.2674 4.01455 10.3049 4.06526C10.4675 4.30608 10.7176 4.45185 10.9865 4.45185H10.9928C11.2679 4.45819 11.5243 4.31876 11.6931 4.07159Z"
                                                        fill="var(--book_about)" />
                                                </svg>
                                                }
                                                <!-- <svg width="22" height="20" viewBox="0 0 22 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M10.7739 2.13863C10.6113 1.9992 10.4613 1.86611 10.3112 1.72668C9.21062 0.68098 8.16635 0.148622 7.03453 0.0535581C6.82818 0.0345453 6.62182 0.0282077 6.42172 0.0282077C4.47075 0.0282077 2.85118 0.826744 1.5818 2.41748C0.262383 4.07159 -0.231615 6.04892 0.0998015 8.29877C0.368686 10.1177 1.15033 11.6197 2.41972 12.7604C4.35194 14.4906 6.34043 16.2208 8.2664 17.8875C8.97926 18.5086 9.69211 19.1234 10.405 19.7445C10.5113 19.8395 10.6238 19.9092 10.7301 19.9473C11.0365 20.0613 11.3179 19.9916 11.6118 19.7255C12.7061 18.7685 13.8004 17.8115 14.901 16.8545C15.6514 16.1954 16.408 15.5426 17.1584 14.8835C17.421 14.6554 17.6899 14.4336 17.9463 14.2117C18.5403 13.7111 19.1531 13.1977 19.7034 12.6337C21.6544 10.6183 22.3672 8.21004 21.8232 5.48488C21.3605 3.17799 20.1224 1.54289 18.1464 0.617604C16.2517 -0.269659 14.457 -0.199946 12.8249 0.820407C12.2121 1.20066 11.6431 1.70767 11.1303 2.32242L11.0678 2.39847L10.999 2.33509C10.924 2.26538 10.8489 2.202 10.7739 2.13863ZM11.6931 4.07159C11.7494 3.9892 11.8057 3.90682 11.862 3.82443C12.0621 3.52656 12.2684 3.22236 12.5185 2.96251C13.8692 1.53656 15.989 1.26404 17.7899 2.29707C19.1218 3.05758 19.941 4.26806 20.2349 5.89048C20.6539 8.24173 19.9535 10.2444 18.1401 11.8351C16.5205 13.2548 14.8697 14.6997 13.2689 16.0877C12.5873 16.6834 11.9057 17.2728 11.2304 17.8685C11.1991 17.9002 11.1616 17.9256 11.1241 17.9573L10.999 18.0587L7.34719 14.8709C6.95949 14.5286 6.55929 14.1864 6.1716 13.8568C5.31492 13.1217 4.42697 12.3612 3.5828 11.5753C2.36344 10.4409 1.66934 8.88816 1.63807 7.2087C1.60681 5.61797 2.17584 4.10328 3.20761 3.05758C4.60206 1.64429 6.81567 1.27038 8.4665 2.16398C9.11683 2.51888 9.6671 3.05124 10.1361 3.79274C10.1611 3.83076 10.1861 3.86879 10.2049 3.90682C10.2361 3.96385 10.2674 4.01455 10.3049 4.06526C10.4675 4.30608 10.7176 4.45185 10.9865 4.45185H10.9928C11.2679 4.45819 11.5243 4.31876 11.6931 4.07159Z"
                                                        fill="var(--st_back)" />
                                                </svg> -->
                                            </span>
                                            <span class="relative tm_rt show_time right-[0] top-[2px]">{{ track.time ?
                                                track.time :
                                                convertSecondsToTime(track.duration)}}
                                            </span>
                                            <span class="relative dots hvr tm_rt show_dots right-[0] top-[2px]">
                                                <svg width="24" height="5" viewBox="0 0 24 5" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="21.3637" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                    <circle cx="11.7499" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                    <circle cx="2.13636" cy="2.13636" r="2.13636"
                                                        fill="var(--font-color1)" />
                                                </svg>
                                                <span class="on_hover py-2">
                                                    <div class="addtnl_sp"></div>
                                                    <!-- <p (click)="remove(i); $event.stopPropagation();"
                                                        class="dropdown-item cat_i">
                                                        Удалить</p> -->
                                                    <p (click)="share(track); $event.stopPropagation();"
                                                        class="dropdown-item cat_i">
                                                        Поделиться
                                                    </p>
                                                    <p title="Добавить в Избранное" class="dropdown-item cat_i"
                                                        [ngClass]="{'in-favourites': tracks[currentTrackIndex]!.id | isInPlaylist : playlists}"
                                                        (click)="showPlaylistDialog(); $event.stopPropagation();">
                                                        <span class="flex">
                                                            Избранное
                                                        </span>
                                                    </p>
                                                </span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <button (click)="closePlayer()" class="cl_bun"><span>Закрыть</span></button>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
}
@if (isBackgroundPlay || !tracks.length || emptyPlayer) {
<div class="app-additional-mobile-menu">
    <div class="add-menu_wrap">
        <div class="side_wrap">
            <div (click)="navigateToLibrary()" class="side_item fst_">
                <div class="img_item"></div>
                <span>Cтатьи</span>
            </div>
            <div class="side_item scd_">
                <div class="img_item"></div>
                <span>Практики</span>
            </div>
        </div>
        <div (click)="toggleSidebar();$event.stopPropagation();" class="center_menu">
            <!-- <span>меню</span> -->
        </div>
        <div class="side_wrap last_one">
            <div [routerLink]="['/', currentLanguage(), 'search']" class="side_item trd_">
                <div class="img_item"></div>
                <span>Поиск</span>
            </div>
            @if (!isBackgroundPlay) {
            <div (click)="navigateToAudio()" class="side_item lst_">
                <div class="img_item"></div>
                <span>Лекции</span>
            </div>
            } @else {
            <div (click)="lockBody(true);isBackgroundPlay = false;" class="side_item lst_pl">
                <div class="img_item"></div>
                <span>Плеер</span>
            </div>
            }
        </div>
    </div>
</div>
}
<div id="dialog-playlist" style="display: none; max-width: 400px; width: 100%">
    <ng-select [items]="playlistService.items" [multiple]="true" [addTag]="true" [(ngModel)]="selectedPlaylists"
        bindLabel="name" [placeholder]="selectedPlaylists.length ? '' : 'Введите название плейлиста'"
        addTagText="Создать плейлист">
    </ng-select>
    <button class="btn-light" style="margin: 0; margin-top: 20px;" (click)="addToPlaylistProfile()">Сохранить</button>
</div>

@if(tracks[currentTrackIndex] && tracks[currentTrackIndex].id) {
<playlist-dialog [showPlaylist]="showPlaylist" [selectedTrackId]="tracks[currentTrackIndex]!.id"
    (playlistClosed)="playlistClosed($event)" (playlistSelected)="playlistSelected($event)">
</playlist-dialog>
}