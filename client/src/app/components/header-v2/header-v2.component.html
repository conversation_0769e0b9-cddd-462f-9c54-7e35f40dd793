<header 
    [class.scrolled]="isScrolled()"
    [class.link-menu-opened]="activeLinkMenu()"
>
    <div class="left-actions">
        <button class="mobile-menu-toggle" (click)="toggleMobileMenu()" [attr.aria-expanded]="isMobileMenuOpen()"
            [attr.aria-label]="isMobileMenuOpen() ? 'Закрыть меню' : 'Открыть меню'" type="button">
            <div class="burger-icon" [class.active]="isMobileMenuOpen()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </button>

        <a >Главная</a>
        <a (click)="toggleLinkMemu(MenuLinkMenuKeys.tradition)">О традиции</a>
        <a (click)="toggleLinkMemu(MenuLinkMenuKeys.education)">Обучение</a>
        <a (click)="toggleLinkMemu(MenuLinkMenuKeys.practice)">Практика</a>
        <a (click)="toggleLinkMemu(MenuLinkMenuKeys.library)">Библиотека</a>
        <a (click)="toggleLinkMemu(MenuLinkMenuKeys.events)">События</a>
    </div>
    <div class="right-actions">
        <a>Форум</a>
        <a>Поддержать</a>
        <a>Eng ^</a>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 6L12 2L8 2" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M15 11L15 13" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M2 12L4 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M20 12L22 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path
                d="M20 16C20 16.5304 19.7893 17.0391 19.4142 17.4142C19.0391 17.7893 18.5304 18 18 18L8.828 18C8.29761 18.0001 7.78899 18.2109 7.414 18.586L5.212 20.788C5.1127 20.8873 4.9862 20.9549 4.84849 20.9823C4.71077 21.0097 4.56803 20.9956 4.43831 20.9419C4.30858 20.8881 4.1977 20.7971 4.11969 20.6804C4.04167 20.5637 4.00002 20.4264 4 20.286L4 8C4 7.46957 4.21071 6.96086 4.58579 6.58579C4.96086 6.21071 5.46957 6 6 6L18 6C18.5304 6 19.0391 6.21071 19.4142 6.58579C19.7893 6.96086 20 7.46957 20 8L20 16Z"
                stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M9 11L9 13" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>

        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z"
                stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M12 2L12 4" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M12 20L12 22" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M4.92969 4.92969L6.33969 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            <path d="M17.6562 17.6602L19.0662 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            <path d="M2 12L4 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M20 12L22 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M6.33969 17.6602L4.92969 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            <path d="M19.0662 4.92969L17.6562 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
        </svg>

        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.9963 21.0002L16.6562 16.6602" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            <path
                d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>


    </div>
</header>
@if(selectedLinkMenu().length) {
    <div class="link-menu" [class.opened]="activeLinkMenu()">
        <div class="flex flex-wrap gap-[30px] w-full">
            @for (linkMenuItem of selectedLinkMenu(); track linkMenuItem.name) {
                <div class="link-menu-item">
                    <div class="item-img" [style.background-image]="'url(' + linkMenuItem.imgUrl + ')'">
                        <div class="img-mask"></div>
                    </div>
                    <div class="link-menu-item-text-content">
                        <div class="title">{{linkMenuItem.name}}</div>
                        @if(linkMenuItem.description) {
                            <div class="description">
                                {{linkMenuItem.description}}
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
        <div class="show-more">Смотреть больше</div>
    </div>
}