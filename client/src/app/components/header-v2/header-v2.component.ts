import { Component, EventEmitter, Inject, inject, input, Output, PLATFORM_ID, effect, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from "@angular/router";
import { TranslocoService } from "@jsverse/transloco";
import { ContentService } from "@/services/content.service";
import { ToasterService } from '@/services/toaster.service';
import { LibraryService } from '@/services/library.service';
import { ProfileService } from "@/services/profile.service";
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { AuthService } from '@/services/auth.service';
import {environment} from "@/env/environment";
import { ClickOutsideDirective } from "@/directives/clickOutside";

export enum MenuLinkMenuKeys {
  tradition = 'tradition',
  education = 'education',
  practice = 'practice',
  library = 'library',
  events = 'events',
}

@Component({
  selector: 'app-header-v2',
  standalone: true,
  imports: [RouterLink, CommonModule, NgOptimizedImage, ClickOutsideDirective],
  templateUrl: './header-v2.component.html',
  styleUrl: './header-v2.component.scss'
})
export class HeaderV2Component {
  readonly menuLinksArrays: Record<MenuLinkMenuKeys, any[]> = {
    [MenuLinkMenuKeys.tradition]: [
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Санатана Драхма',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Линия передачи',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Гуру',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Символ веры',
        description: '',
        link: ''
      },
    ],
    [MenuLinkMenuKeys.education]: [
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Курсы',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Базовый курс',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Институт Васиштхи',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Система обучения',
        description: '',
        link: ''
      },
      
    ],
    [MenuLinkMenuKeys.practice]: [
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Джняна-йога, Бхакти-йога и т.д',
        description: 'Джняна-йога, Бхакти-йога и т.д',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Ретриты',
        description: 'общие сведения о ритритах',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Институт Мантры и ритуалы Васиштхи',
        description: 'общая статья о мантрах и ритуалах',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Как стать учеником?',
        description: '',
        link: ''
      },

    ],
    [MenuLinkMenuKeys.library]: [
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Курсы',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Базовый курс',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Институт Васиштхи',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Система обучения',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Фотогалерея',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Радио',
        description: '',
        link: ''
      },
    ],
    [MenuLinkMenuKeys.events]: [
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Календарь событий',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Базовый курс',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Институт Васиштхи',
        description: '',
        link: ''
      },
      {
        imgUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        name: 'Семинары',
        description: '',
        link: ''
      },
    ],
  };
  readonly isScrolled = input<boolean>(false);
  readonly isMobileMenuOpen = signal<boolean>(false);
  isUserMenuOpen = signal<boolean>(false);
  activeLinkMenu = signal<MenuLinkMenuKeys | null>(null);

  selectedLinkMenu = signal<any[]>([]);
  readonly MenuLinkMenuKeys = MenuLinkMenuKeys;



  // searchShow: boolean = false;
  @Output() sideOpen = new EventEmitter<any>();
  @Output() sideClose = new EventEmitter<any>();
  // @Input() isSidebarOpen = false;
  route = inject(ActivatedRoute)
  router = inject(Router);
  contentService = inject(ContentService);
  libraryService = inject(LibraryService);
  translocoService = inject(TranslocoService);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  currentLanguage = signal<string>('ru');
  environment = environment;
  

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) {
    effect(() => {
      if (this.authService.token() && !this.profileService.name()) {
        this.profileService.getProfile().subscribe();
      } 
    });
   }

  ngOnInit() {
    // this.contentService.getAll().subscribe();
    // this.libraryService.getAll().subscribe();
  }

  // toggleSearchPanel() {
  //   this.searchShow = !this.searchShow;
  // }

  async changeLanguage(e: any) {
    const currentUrl = this.router.url
    const newUrl = currentUrl.replace(`/${this.translocoService.getActiveLang()}/`, `/${e.target.value}/`)
    this.router.navigate([newUrl])
  }

  toggleSidebar() {
    this.sideOpen.emit(true);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
  }


  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
  }

  navigateToMain() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/`]);
    this.sideOpen.emit(false);
  }

  navigateToCatigory() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/categories`]);
    this.sideOpen.emit(false);
  }

  toggleUserMenu(event: Event) {
    event.stopPropagation();
    this.isUserMenuOpen.set(!this.isUserMenuOpen());
  }

  closeUserMenu() {
    this.isUserMenuOpen.set(false);
  }

  navigateToProfile() {
    this.router.navigate(['/ru/profile']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToFavorites() {
    this.router.navigate(['/ru/profile/favorites']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToPlaylists() {
    this.router.navigate(['/ru/profile/playlists']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToMyData() {
    this.router.navigate(['/ru/profile/my-data']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToAnketa() {
    this.router.navigate(['/ru/anketa']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToSubscriptions() {
    this.router.navigate(['/ru/profile/subscriptions']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  logout() {
    this.authService.logout();
    this.closeUserMenu();
    this.sideClose.emit(true);
  }









  toggleLinkMemu(key: MenuLinkMenuKeys) {
    if (this.activeLinkMenu() === key) {
      this.activeLinkMenu.set(null)
      this.selectedLinkMenu.set([]);
    } else {
      this.activeLinkMenu.set(key)
      this.selectedLinkMenu.set(this.menuLinksArrays[key]);
    }
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen.set(!this.isMobileMenuOpen());
  }
}
