@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';

header {
    height: 108px;
    position: fixed;
    top: 0;
    width: 100%;
    background-color: transparent;
    padding: 16px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    z-index: 999;

    &.scrolled {
        background-color: main(50);
        // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        transition: background-color 0.25s ease-in-out;
    }
    &.link-menu-opened {
        background-color: main(50);
    }

    .left-actions {
        display: flex;
        align-items: center;
        gap: 24px;

        .mobile-menu-toggle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .burger-icon {
                width: 16px;
                height: 14px;
                position: relative;
                transition: 0.5s ease-in-out;

                span {
                    display: block;
                    position: absolute;
                    height: 2px;
                    width: 100%;
                    background: main(700);
                    border-radius: 2px;
                    left: 0;
                    transition: 0.25s ease-in-out;

                    &:nth-child(1) {
                        top: 0;
                    }

                    &:nth-child(2) {
                        top: 6px;
                    }

                    &:nth-child(3) {
                        top: 12px;
                    }
                }

                &.active span {
                    &:nth-child(1) {
                        top: 6px;
                        transform: rotate(135deg);
                    }

                    &:nth-child(2) {
                        opacity: 0;
                        left: -60px;
                    }

                    &:nth-child(3) {
                        top: 6px;
                        transform: rotate(-135deg);
                    }
                }
            }
        }
    }

    .right-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
    }

    a {
        position: relative;
        color: main(700);
        text-decoration: none;
        cursor: pointer;

        &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            right: 0;
            width: 0;
            height: 2px;
            background-color: main(600);
            transition: width 0.2s ease-in-out;
        }

        &:hover::after {
            width: 100%;
            left: 0;
            right: auto;
        }
    }

}
.link-menu {
    height: fit-content;
    max-height: 0px;
    position: fixed;
    top: 108px;
    width: 100%;
    background-color: main(50);
    padding: 48px 15px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    box-shadow: 0px 1px 5.8px 0px #42381E33;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;
    z-index: 998;
    &.opened {
        opacity: 1;
        transform: translateY(0);
        min-height: 282px;
        max-height: 100%;
    }
    .link-menu-item {
        display: flex;
        gap: 20px;
        flex-basis: calc(25% - 23px);
        max-width: calc(25% - 23px);
        .item-img {
            width: 90px;
            height: 90px;
            background-size: cover;
            position: relative;
            .img-mask {
                background: url(../../../assets/images/main-v2/head-menu-mask.webp) no-repeat center;
                background-size: contain;
                width: 90px;
                height: 90px;
                position: sticky;
                z-index: 2;
            }
        }
        .link-menu-item-text-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 16px;
            .title {
                @include body-1;
                color: main(700);
            }
            .description {
                @include body-2;
                color: main(500);

            }
        }
    }
    .show-more {
        cursor: pointer;
        @include button-1;
        color: main(700);
    }
}
