import { environment } from "@/env/environment"
import { CommonModule, DOCUMENT, isPlatformBrowser, PlatformLocation } from '@angular/common'
import { Component, DestroyRef, EventEmitter, inject, Inject, Input, Output, PLATFORM_ID, RendererFactory2 } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { ActivatedRoute, Router } from "@angular/router"
import { timer } from 'rxjs'

export class PhotoActionIcon {
  order?: number;
  action: (item: any) => void = () => { };
}

export class ActionsSettings {
  viewIcon?: PhotoActionIcon;
  favoritIcon?: PhotoActionIcon;
  shareIcon?: PhotoActionIcon;
  deleteIcon?: PhotoActionIcon;
  likeIcon?: PhotoActionIcon;
}

@Component({
  selector: 'app-photo-gallery',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './photo-gallery.component.html',
  styleUrl: './photo-gallery.component.scss'
})
export class PhotoGalleryComponent {
  private _images: any[] = [];
  @Input()
  set images(value: any[]) {
    this._images = value;
    this.distributeImages();
    // Check for photo parameter when images are loaded
    this.checkPhotoParameter();
  }
  get images(): any[] {
    return this._images;
  }
  @Input() customPhotoActionsClasses: string = '';
  @Input() actionsSettings: ActionsSettings = this.getDefaultActions();
  @Input() favourites: any = [];
  @Output() onPhotoClick: EventEmitter<any> = new EventEmitter();
  selectedPhotoUrl: string = '';
  init: boolean = true;
  page: boolean = false;

  selectedPhoto: any;
  private readonly destroyRef = inject(DestroyRef);
  router = inject(Router);
  route = inject(ActivatedRoute);
  fast$ = timer(500);
  renderer;
  col1Images: any[] = [];
  col2Images: any[] = [];
  col3Images: any[] = [];

  protected readonly environment = environment;
  constructor(
    private rendererFactory: RendererFactory2,
    @Inject(PLATFORM_ID) private platformId: Object,
    @Inject(DOCUMENT) private document: Document,
    private platformLocation: PlatformLocation
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.renderer = this.rendererFactory.createRenderer(null, null);
      this.renderer.listen('document', 'keydown', (event) => this.handleClick(event));
      this.renderer.listen('document', 'wheel', (event) => this.handleWheel(event));
    }
  }

  ngOnInit() {

    // Listen for query parameter changes to handle photo parameter
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      const photoId = params['photo'];
      if (photoId && this.images && this.images.length > 0) {
        this.openPhotoFromUrl(photoId);
      }
    });

    if (isPlatformBrowser(this.platformId)) {
      this.platformLocation.onPopState(() => {
        const photoIdToScrollTo = this.selectedPhoto?.id;
        this.selectedPhoto = null;
        this.selectedPhotoUrl = '';
        this.init = true;

        // Scroll to the photo that was just closed
        if (photoIdToScrollTo) {
          setTimeout(() => {
            this.scrollToPhoto(photoIdToScrollTo);
          }, 100);
        }
      });
    }
  }

  getBackgroundImageUrl(imageName: string | undefined): string {
    if (!imageName) return '';
    return `${environment.serverUrl}/upload/${imageName}`;
  }

  defaultFunction(item: any): void {
    console.log('Default action triggered with item:', item);
  }

  distributeImages() {
    this.col1Images = [];
    this.col2Images = [];
    this.col3Images = [];

    this.images.forEach((image, index) => {
      const colIndex = index % 3;
      switch (colIndex) {
        case 0:
          this.col1Images.push(image);
          break;
        case 1:
          this.col2Images.push(image);
          break;
        case 2:
          this.col3Images.push(image);
          break;
      }
    });
  }


  private getDefaultActions(): ActionsSettings {
    return {
      favoritIcon: {
        action: (item: any) => this.defaultFunction(item)
      },
      shareIcon: {
        action: (item: any) => this.defaultFunction(item)
      },
      likeIcon: {
        action: (item: any) => this.defaultFunction(item)
      }
    };
  }

  inFavourites(id: number): boolean {
    return this.favourites.includes(id)
  }


  handleClick(event: any) {
    if (event.code == 'Escape') {
      if (this.selectedPhoto) {
        this.minimizePhoto();
      }
    }
    if (event.code == 'ArrowRight' || event.code == 'KeyD' || event.code == 'Space') {
      this.nextPhoto();
    }
    if (event.code == 'ArrowLeft' || event.code == 'KeyA') {
      this.prewPhoto();
    }
  }

  handleWheel(event: WheelEvent) {
    // Only handle wheel events when a photo is selected (fullscreen mode)
    if (!this.selectedPhoto) return;

    // Prevent default scrolling behavior
    event.preventDefault();

    // Check wheel direction and switch photos accordingly
    if (event.deltaY > 0) {
      // Scrolling down - go to next photo
      this.nextPhoto();
    } else if (event.deltaY < 0) {
      // Scrolling up - go to previous photo
      this.prewPhoto();
    }
  }

  showFullscreenPhoto(item: any) {
    this.showPhoto(item);
  }

  showPhoto(item: any) {
    this.selectedPhoto = item;
    this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${item.name}`;

    // Add photo parameter to URL
    this.router.navigate([], {
      queryParams: { photo: item.id },
      queryParamsHandling: 'merge'
    });

    this.fast$.subscribe(() => {
      this.init = false;
      this.setBody();
    });
  }

  openPhotoFromUrl(photoId: string | number) {
    const photo = this.images.find((img: any) => img.id == photoId);
    if (photo) {
      this.selectedPhoto = photo;
      this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${photo.name}`;
      this.fast$.subscribe(() => {
        this.init = false;
        this.setBody();
      });
    }
  }

  checkPhotoParameter() {
    if (this.images && this.images.length > 0) {
      const photoId = this.route.snapshot.queryParams['photo'];
      if (photoId) {
        this.openPhotoFromUrl(photoId);
      }
    }
  }

  nextPhoto() {
    if (!this.selectedPhoto) return;

    // const filename = 'photo/' + this.selectedPhoto.split('/').pop();
    let existPhotoIndex = this.images.findIndex((image: any) => image.id == this.selectedPhoto.id);
    if (existPhotoIndex || existPhotoIndex == 0) {
      if (this.images?.length - 1 == existPhotoIndex) {
        this.selectedPhoto = this.images[0];
        this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${this.images[0]['name']}`;
      } else {
        this.selectedPhoto = this.images[existPhotoIndex + 1];
        this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${this.images[existPhotoIndex + 1]['name']}`;
      }

      // Update URL parameter with new photo ID
      this.router.navigate([], {
        queryParams: { photo: this.selectedPhoto.id },
        queryParamsHandling: 'merge'
      });
    }
    this.page = true;
    this.fast$.subscribe(() => {
      this.page = false;
    });
    this.setBody();
  }

  closeSelectedPhoto(event: Event) {
    const targetElement = event.target as HTMLElement;
    const selectedPhoto = document.querySelector('.photo-container-item');
    if (selectedPhoto && !selectedPhoto.contains(targetElement) && !this.page) {
      if (!this.init) {
        this.minimizePhoto();
      }
    }
  }

  prewPhoto() {
    if (!this.selectedPhoto) return;
    // const filename = 'photo/' + this.selectedPhoto.split('/').pop();
    let existPhotoIndex = this.images.findIndex((image: any) => image.id === this.selectedPhoto.id);
    if (existPhotoIndex !== -1) {
      if (existPhotoIndex === 0) {
        this.selectedPhoto = this.images[this.images.length - 1];
        this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${this.images[this.images.length - 1]['name']}`;
      } else {
        this.selectedPhoto = this.images[existPhotoIndex - 1];
        this.selectedPhotoUrl = `${this.environment.serverUrl}/upload/${this.images[existPhotoIndex - 1]['name']}`;
      }

      // Update URL parameter with new photo ID
      this.router.navigate([], {
        queryParams: { photo: this.selectedPhoto.id },
        queryParamsHandling: 'merge'
      });
    }
    this.page = true;
    this.fast$.subscribe(() => {
      this.page = false;
    });
    this.setBody();
  }

  minimizePhoto() {
    const photoIdToScrollTo = this.selectedPhoto?.id;

    this.selectedPhoto = null;
    this.selectedPhotoUrl = '';
    this.init = true;

    // Remove photo parameter from URL
    this.router.navigate([], {
      queryParams: { photo: null },
      queryParamsHandling: 'merge'
    });

    this.setBody();

    // Scroll to the photo that was just closed
    if (photoIdToScrollTo && isPlatformBrowser(this.platformId)) {
      setTimeout(() => {
        this.scrollToPhoto(photoIdToScrollTo);
      }, 100);
    }
  }

  setBody() {
    if (isPlatformBrowser(this.platformId)) {
      const body = this.document.body;
      // if (this.selectedPhoto) {
      //   this.renderer2.setStyle(body, 'overflow', 'hidden');
      // } else {
      //   this.renderer2.setStyle(body, 'overflow', 'auto');
      // }
    }
  }

  private scrollToPhoto(photoId: number | string) {
    if (!isPlatformBrowser(this.platformId)) return;

    const photoElement = this.document.getElementById(`photo-card-${photoId}`);
    if (photoElement) {
      photoElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    }
  }
}
