FROM node:22-alpine as base
WORKDIR /client
COPY package*.json ./
RUN apk add make gcc g++ python3 curl
RUN npm ci --force
COPY . .

FROM base as production
RUN npm run build
EXPOSE 9019
CMD ["npm", "run", "serve:ssr:client"]

FROM base as development
RUN npm run build:dev:ssr
EXPOSE 9019
CMD ["npm", "run", "serve:ssr:client"]

FROM base as local
EXPOSE 9019
CMD ["npm", "run", "start:local"]

FROM base as local-ssr
RUN npm run build:local:ssr
EXPOSE 9019
CMD ["npm", "run", "serve:ssr:client"]

FROM base as development-ssr
RUN npm run build:dev:ssr
EXPOSE 9019
CMD ["npm", "run", "serve:ssr:client"]