import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {ContentService} from "@/services/content.service";
import {ActivatedRoute, Router} from "@angular/router";
import { isPlatformBrowser, Location, CommonModule } from '@angular/common';
import { Inject, PLATFORM_ID } from '@angular/core';
import {PhotopreviewComponent} from "@/components/photopreview/photopreview.component";
import {FileService} from "@/services/file.service";

@Component({
    selector: 'app-category-add',
    imports: [
        ReactiveFormsModule, CommonModule, PhotopreviewComponent
    ],
    templateUrl: './category-add.component.html',
    styleUrl: './category-add.component.scss'
})
export class CategoryAddComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  fb = inject(FormBuilder)
  route = inject(ActivatedRoute)
  router = inject(Router)
  fileService = inject(FileService)
  contentService = inject(ContentService)
  categoryAddForm = this.fb.group({
    id: null,
    title: ['', Validators.required],
    active: [true],
    preview: [null, Validators.required],
  })
  id = this.route.snapshot.params['id'];
  spinner: boolean = false;

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) {}

  ngOnInit() {
    if(this.id && this.id !== 'add') {
      this.contentService.getCategory(this.id).subscribe((res: any) => this.categoryAddForm.patchValue(res))
    }
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  categoryAdd() {
    console.log(this.categoryAddForm.valid)
    if(!this.categoryAddForm.valid) return;
    this.contentService.addCategory(this.categoryAddForm.value).subscribe(() => {
      this.openDialog('Категория создана.')
    })
  }

  close(dialog: any) {
    dialog.close();
    if (this.message == 'Категория создана.') {
      this.router.navigate(['/content']);
    }
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal()
  }

  uploadPreview(e: Event) {
    this.spinner = true;
    const files = (e.target as HTMLInputElement).files!

    this.fileService.uploadToTempFolder(files).subscribe((res: any) => {
      this.categoryAddForm.get('preview')?.setValue(res[0])
      this.spinner = false;
    })
  }
}
