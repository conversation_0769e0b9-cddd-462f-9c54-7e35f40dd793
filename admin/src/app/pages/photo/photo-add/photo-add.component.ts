import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from "@angular/forms";
import { Inject, PLATFORM_ID } from '@angular/core';
import { PhotoService } from "@/services/photo.service";
import { ActivatedRoute, Router } from "@angular/router";
import { FileService } from "@/services/file.service";
import { CommonModule, NgClass } from "@angular/common";
import { environment } from "../../../../environments/environment";
import { PhotopreviewComponent } from "@/components/photopreview/photopreview.component";
import { isPlatformBrowser, Location } from '@angular/common';
import { LanguagesEnum } from "@/enums/languages.enum";

@Component({
    selector: 'app-photo-add',
    imports: [
        ReactiveFormsModule,
        PhotopreviewComponent,
        NgClass,
        CommonModule
    ],
    templateUrl: './photo-add.component.html',
    styleUrl: './photo-add.component.scss'
})
export class PhotoAddComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  photoService = inject(PhotoService)
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  fileService = inject(FileService)
  isUploadingPhotos: boolean = false;
  isUploadingCover: boolean = false;
  isSubmitting = false;
  fields = {
    id: [null],
    title: [null],
    seo_title: [null],
    seo_description: [null],
    cover: [null],
    photos: []
  }
  spinner: boolean = false;
  spinner1: boolean = false;
  forms: { lang: string, form: FormGroup }[] = []
  files: any = {}
  id: number | null = this.route.snapshot.params['id']
  languages = Object.values(LanguagesEnum)
  selectedLanguage = 'ru'

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) { }

  ngOnInit() {
    for (let lang of this.languages) {
      this.forms.push({
        lang, form: this.fb.group(this.fields)
      })
      this.files[lang] = {
        cover: [],
        photos: []
      }
    }
    if (this.id) {
      this.photoService.get(this.id).subscribe((res: any) => {
        for (let item of res.translations) {
          const form = this.forms.find((e: any) => e.lang == item.lang)?.form
          form?.patchValue(item)
          this.files[item.lang] = {
            cover: item.cover,
            photos: item.photos || []
          }
        }
      })
    }
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
    this.router.navigate(['/photo'])
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  uploadFile(e: Event, name: string, multiple: boolean = true) {
    const target = e.target as HTMLInputElement;
    if (!target.files) return

    if (name === 'cover') {
      this.isUploadingCover = true;
      this.spinner = true;
    } else {
      this.isUploadingPhotos = true;
      this.spinner1 = true;
    }

    this.fileService.uploadToTempFolder(target.files).subscribe({
      next: (res: any) => {
            for (let lang of this.languages) {
              if (!this.files[lang]) {
                this.files[lang] = {};
              }
              if (name === 'cover') {
                this.files[lang][name] = res[0];
              } else {
                this.files[lang][name] = [...(this.files[lang][name] || []), ...res]
              }
            }
      },
        complete: () => {
          if (name === 'cover') {
            this.isUploadingCover = false;
            this.spinner = false;
          } else {
            this.isUploadingPhotos = false;
            this.spinner1 = false;
          }
        }
    })

    // this.fileService.upload(target.files, 'photo').subscribe({
    //   next: (res: any) => {
    //     for (let lang of this.languages) {
    //       if (!this.files[lang]) {
    //         this.files[lang] = {};
    //       }
    //       if (name === 'cover') {
    //         this.files[lang][name] = res[0];
    //       } else {
    //         this.files[lang][name] = multiple ? res : [res[0]];
    //       }
    //     }
    //   },
    //   complete: () => {
    //     if (name === 'cover') {
    //       this.isUploadingCover = false;
    //     } else {
    //       this.isUploadingPhotos = false;
    //     }
    //   }
    // })


  }

  onSubmit() {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    const form = this.forms.map(e => ({ ...e, form: { ...e.form.value, ...this.files[e.lang] } }))
    if (this.id) {
      this.photoService.update(this.id, form).subscribe({
        next: (res: any) => {
          //location.reload()
          this.openModal('Успешно обновлено!');
        },
        error: () => {
          this.isSubmitting = false;
          this.openModal('Ошибка!');
        },
        complete: () => {
          this.isSubmitting = false;
        }
      })
      return
    }
    this.photoService.create(form).subscribe({
      next: () => {
        this.openModal('Операция выполнена успешно!');
      },
      error: () => {
        this.isSubmitting = false;
        this.openModal('Ошибка!');
      },
      complete: () => {
        this.isSubmitting = false;
      }
    })
    return false
  }

  deletePhoto(id: number) {
    return this.photoService.deletePhoto(id).subscribe(() => location.reload())
  }

  selectLanguage(lang: string) {
    this.selectedLanguage = lang
  }

  removeFile(id: any) {
    for (let {form, lang} of this.forms) {
      if(form.value.cover && form.value.cover.id == id) {
        form.setControl('cover', null)
        this.files[lang]['cover'] = null
      }
    }
  }

  protected readonly environment = environment;
}
