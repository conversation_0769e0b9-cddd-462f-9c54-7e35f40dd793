import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {AudioService} from "@/services/audio.service";
import {ActivatedRoute} from "@angular/router";
import {environment} from "../../../environments/environment";
import {CommonModule, JsonPipe} from "@angular/common";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule} from "@angular/forms";
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectModule } from 'primeng/select';
import {AudiofilesService} from "@/services/audiofiles.service";

@Component({
  selector: 'app-audio',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    FormsModule,
    TableModule,
    MultiSelectModule,
    SelectModule,
    JsonPipe
  ],
  templateUrl: './audiofiles.component.html',
  styleUrl: './audiofiles.component.scss'
})
export class AudiofilesComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  route = inject(ActivatedRoute);
  audiofilesService = inject(AudiofilesService)
  protected readonly environment = environment;
  page = this.route.snapshot.params['page']
  fb = inject(FormBuilder)
  authors: any = []
  selectedAudio: any = null;
  filters = this.fb.group({
    page: [1],
    title: [null],
    types: [null],
    singers: [null],
    tags: [null],
    sort: ['views']
  })
  columns =  [
    { field: 'title', header: 'Название' },
    { field: 'type', header: 'Тип' },
    { field: 'singer', header: 'Исполнитель' },
    { field: 'tags', header: 'Теги' },
    { field: 'description', header: 'Описание' },
    { field: 'comment', header: 'Комментарий' },
    { field: 'duration', header: 'Длительность, сек.' },
    { field: 'likes', header: 'Лайки' },
    { field: 'favourites', header: 'Избранное' },
  ];
  selectedColumns = this.columns;


  items: any = []
  singers: any = []
  types: any = []
  tags: any = []
  favourites: any = []

  get filterValues() {
    return Object.fromEntries(
      Object.entries(this.filters.value).filter(([key, value]) => value !== null)
    )
  }

  get getTagsName() {
    return (arr: any) => arr.map((e: any) => e.name).join(', ');
  }

  ngOnInit() {
    this.findAll()
    this.audiofilesService.getSingers().subscribe((res: any) => this.singers = res)
    this.audiofilesService.getTypes().subscribe((res: any) => this.types = res)
    this.audiofilesService.getTags().subscribe((res: any) => this.tags = res)

    this.audiofilesService.getPlaylists().subscribe((res: any) => this.favourites = res)
  }

  findAll() {
    this.audiofilesService.findAll(this.filterValues).subscribe((res: any) => {
      this.items = res.items;
      this.getPagination()
    })
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  applyFilter() {
    this.findAll()
  }
  getPagination() {
    const arr = []
    const currentPage = this.filters.value.page!
    const pageStart = currentPage-2 <= 1 ? 1 : currentPage-3
    const pageEnd = currentPage+3
    for(let i = pageStart; i <= pageEnd; i++) arr.push(i)
    return arr
  }
  changePage(page: number) {
    this.filters.patchValue({page: page});
    this.applyFilter()
  }

  openAudioPlayer(audio: any, modal: HTMLDialogElement): void {
    this.selectedAudio = audio;
    modal.showModal();
  }

  closeAudioModal(modal: HTMLDialogElement): void {
    modal.close();
    this.selectedAudio = null;
  }

  favouritesCount(id: number) {
    return this.favourites.filter((e: any) => e.audioFile.id === id).length
  }
}
