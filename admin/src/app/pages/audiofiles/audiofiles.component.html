<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Аудиофайлы</h1>
    </div>
    <div class="admin-actions">
      <!-- <button class="btn btn-primary">
        Импорт
        <input type="file" (change)="import($event)" class="hidden">
      </button> -->
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <!-- Audio Player Modal -->
  <dialog #audioPlayerModal class="admin-modal" style="min-width: 500px;">
    <div class="admin-modal-content">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">{{selectedAudio?.title}}</h3>
        <button class="text-gray-500 hover:text-gray-700 text-xl" (click)="closeAudioModal(audioPlayerModal)">×</button>
      </div>
      <div class="mb-4">
        <audio controls *ngIf="selectedAudio" class="w-full">
          <source [src]="selectedAudio.url">
          Your browser does not support the audio element.
        </audio>
      </div>
      <div class="admin-modal-footer">
        <button class="btn btn-outline-secondary" (click)="closeAudioModal(audioPlayerModal)">Закрыть</button>
      </div>
    </div>
  </dialog>

  <!-- Filters -->
  <form [formGroup]="filters" class="admin-filters">
    <div class="admin-filter-item">
      <label class="admin-filter-label">
        Название
        <span title="Поиск по названию аудиофайла" class="cursor-pointer ml-1 text-gray-400">&#9432;</span>
      </label>
      <input formControlName="title" type="text" class="form-input" (input)="applyFilter()" placeholder="Введите название">
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Тип</label>
      <p-select
        [options]="types"
        formControlName="types"
        optionValue="id"
        optionLabel="name"
        [showClear]="true"
        placeholder="Все типы"
        (onChange)="applyFilter()"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Исполнитель</label>
      <p-select
        [options]="singers"
        formControlName="singers"
        optionValue="id"
        optionLabel="name"
        [showClear]="true"
        placeholder="Все исполнители"
        (onChange)="applyFilter()"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Тег</label>
      <p-select
        [options]="tags"
        formControlName="tags"
        optionValue="id"
        optionLabel="name"
        [showClear]="true"
        placeholder="Все теги"
        (onChange)="applyFilter()"
        class="w-full">
      </p-select>
    </div>
  </form>

  <!-- Content -->
  <div class="admin-content-wrapper admin-table">
    <p-table
    [columns]="selectedColumns"
    [value]="items"
    dataKey="id"
    [scrollable]="true"
    [resizableColumns]="true"
    [reorderableColumns]="true"
    columnResizeMode="expand"
    styleClass="p-datatable-gridlines"
    scrollHeight="800px"
    [tableStyle]="{'min-width': '10rem'}"
    [paginator]="true"
    [rows]="50"
    [rowsPerPageOptions]="[50, 100, 200]">
    <ng-template #caption>
      <p-multiselect
        display="chip"
        [options]="columns"
        [(ngModel)]="selectedColumns"
        optionLabel="header"
        selectedItemsLabel="{0} columns selected"
        [style]="{'min-width': '200px'}"
        placeholder="Choose Columns" />
    </ng-template>
    <ng-template #header let-columns>
      <tr>
        <th *ngFor="let col of columns" pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
          {{col.header}} <p-sortIcon [field]="col.field" />
        </th>

        <th style="width: 5rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-audio let-columns="columns">
      <tr >
        <td *ngFor="let col of columns"
            (click)="openAudioPlayer(audio, audioPlayerModal)" class="cursor-pointer">
          @switch (col.field) {
            <!-- @case ('link') {
              <audio controls>
                <source src="{{audio.link}}">
              </audio>
            } -->
            @case ('likes') {
              {{audio.likes}}
            }
            @case ('favourites') {
              {{favouritesCount(audio.id)}}
            }
            @case('tags') {
              {{getTagsName(audio[col.field])}}
            }
            @default {
              {{audio[col.field]?.name || audio[col.field]}}
            }
          }
        </td>

      </tr>
    </ng-template>
    <ng-template #emptymessage>
      <tr>
        <td colspan="6">Нет данных</td>
      </tr>
    </ng-template>
    </p-table>
  </div>
</div>
