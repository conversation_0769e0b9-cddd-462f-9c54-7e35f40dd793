import { AudioService } from "@/services/audio.service"
import { CommonModule } from "@angular/common"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule } from "@angular/forms"
import { ActivatedRoute } from "@angular/router"
import { MultiSelectModule } from 'primeng/multiselect'
import { SelectModule } from 'primeng/select'
import { TableModule } from 'primeng/table'
import { environment } from "../../../environments/environment"

@Component({
    selector: 'app-audio',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        FormsModule,
        TableModule,
        MultiSelectModule,
        SelectModule
    ],
    templateUrl: './audio.component.html',
    styleUrl: './audio.component.scss'
})
export class AudioComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  route = inject(ActivatedRoute);
  audioService = inject(AudioService)
  protected readonly environment = environment;
  page = this.route.snapshot.params['page']
  fb = inject(FormBuilder)
  authors: any = []
  selectedAudio: any = null;
  filters = this.fb.group({
    page: [1],
    title: [null],
    author: [null],
    sort: ['views']
  })
  selectedColumns =  [
    { field: 'title', header: 'Название' },
    { field: 'author', header: 'Автор' },
    { field: 'date', header: 'Дата' },
    { field: 'status', header: 'Статус' },
    // { field: 'link', header: 'Плеер' },
    { field: 'views', header: 'Просмотры' },
    { field: 'likes', header: 'Лайки' },
    { field: 'favourites', header: 'Избранное' },
    { field: 'listened', header: 'Прослушано' },
  ];

  cols = [
    { field: 'title', header: 'Название' },
    { field: 'author', header: 'Автор' },
    { field: 'date', header: 'Дата' },
    { field: 'status', header: 'Статус' },
    // { field: 'link', header: 'Плеер' },
    { field: 'views', header: 'Просмотры' },
    { field: 'likes', header: 'Лайки' },
    { field: 'favourites', header: 'Избранное' },
    { field: 'listened', header: 'Прослушано' },
  ]

  get filterValues() {
    return Object.fromEntries(
      Object.entries(this.filters.value).filter(([key, value]) => value !== null)
    )
  }

  ngOnInit() {
    this.audioService.getAll(this.filterValues).subscribe(() => this.getPagination())
    this.audioService.getAuthors().subscribe((res: any) => this.authors = res)
  }
  import(e: Event) {
    const file = (e.target as HTMLInputElement).files![0]
    const formData = new FormData()
    formData.append('file', file)
    this.audioService.importFromFile(formData).subscribe(() => {
      this.audioService.getAll(this.filterValues).subscribe()
      this.openModal('Импорт успешно завершен')
    })
  }
  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }
  open(item: any) {
    window.open(this.environment.clientUrl + 'ru' + '/audiogallery/audiolektsii/' + item.external_id)
  }
  applyFilter() {
    this.audioService.getAll(this.filterValues).subscribe()
  }
  getPagination() {
    const arr = []
    const currentPage = this.filters.value.page!
    const pageStart = currentPage-2 <= 1 ? 1 : currentPage-3
    const pageEnd = currentPage+3
    for(let i = pageStart; i <= pageEnd; i++) arr.push(i)
    return arr
  }
  changePage(page: number) {
   this.filters.patchValue({page: page});
   this.applyFilter()
  }

  openAudioPlayer(audio: any, modal: HTMLDialogElement): void {
    this.selectedAudio = audio;
    modal.showModal();
  }
  
  closeAudioModal(modal: HTMLDialogElement): void {
    modal.close();
    this.selectedAudio = null;
  }
}
