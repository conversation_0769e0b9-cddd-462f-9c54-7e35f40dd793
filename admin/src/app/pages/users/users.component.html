<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Пользователи</h1>
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Ok</button>
      </div>
    </div>
  </dialog>

  <dialog #confirmDialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{ message }}</div>
      <div class="admin-modal-footer">
        <button class="btn btn-danger">Да</button>
        <button class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <!-- Filters -->
  <div class="admin-filters">
    <div class="admin-filter-item">
      <label class="admin-filter-label">Поиск по имени, email</label>
      <input type="text" class="form-input" [(ngModel)]="searchTerm" placeholder="Введите имя или email">
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Группа</label>
      <p-select
        [options]="[
          {label: 'Не выбрано', value: ''},
          {label: 'Администраторы сайта', value: 'ADMIN'},
          {label: 'Администраторы форума', value: 'FORUM_ADMIN'},
          {label: 'Модераторы форума', value: 'FORUM_MODERATOR'},
          {label: 'Посетители', value: 'VISITOR'},
          {label: 'Ученики', value: 'STUDENT'},
          {label: 'Личные сайты', value: 'PERSONAL_SITE'},
          {label: 'Работа со списком учеников', value: 'STUDENT_LIST'},
          {label: 'Новые анкеты', value: 'NEW'}
        ]"
        [(ngModel)]="groupQuery"
        optionValue="value"
        optionLabel="label"
        [showClear]="true"
        placeholder="Не выбрано"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Статус</label>
      <p-select
        [options]="[
          {label: 'Не выбрано', value: ''},
          {label: 'Новичок', value: 'NEW'},
          {label: 'Заочный символ Веры Санатана Дхармы', value: 'ZSVD'},
          {label: 'Очный символ Веры Санатана Дхармы', value: 'OSVD'},
          {label: 'Заочный символ Веры Прибежища', value: 'ZSVP'},
          {label: 'Очный символ Веры Прибежища', value: 'OSVP'}
        ]"
        [(ngModel)]="statusQuery"
        optionValue="value"
        optionLabel="label"
        [showClear]="true"
        placeholder="Не выбрано"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Подтвержден</label>
      <p-select
        [options]="[
          {label: 'Не выбрано', value: ''},
          {label: 'Да', value: 'true'},
          {label: 'Нет', value: 'false'}
        ]"
        [(ngModel)]="confirmedQuery"
        optionValue="value"
        optionLabel="label"
        [showClear]="true"
        placeholder="Не выбрано"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Активность</label>
      <p-select
        [options]="[
          {label: 'Не выбрано', value: ''},
          {label: 'Да', value: 'true'},
          {label: 'Нет', value: 'false'}
        ]"
        [(ngModel)]="activeQuery"
        optionValue="value"
        optionLabel="label"
        [showClear]="true"
        placeholder="Не выбрано"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Подписка</label>
      <p-select
        [options]="[
          {label: 'Не выбрано', value: ''},
          {label: 'Аудио', value: 'AUDIO'},
          {label: 'Библиотека', value: 'LIBRARY'},
          {label: 'Аудио + Библиотека', value: 'AUDIO_AND_LIBRARY'},
          {label: 'Аудио + Курсы', value: 'AUDIO_AND_COURSES'},
          {label: 'Библиотека + Курсы', value: 'LIBRARY_AND_COURSES'},
          {label: 'Полный доступ', value: 'FULL_ACCESS'}
        ]"
        [(ngModel)]="subscriptionQuery"
        optionValue="value"
        optionLabel="label"
        [showClear]="true"
        placeholder="Не выбрано"
        class="w-full">
      </p-select>
    </div>

    <div class="admin-filter-item">
      <label class="admin-filter-label">Страна</label>
      <p-select [options]="countries" [(ngModel)]="countryQuery" optionValue="iso_code2" optionLabel="name_ru" [filter]="true" filterBy="name_ru" [showClear]="true" placeholder="Выберите страну" class="w-full">
        <ng-template #selectedItem let-selectedOption>
            <div class="flex items-center gap-2">
                <img [src]="selectedOption.flag_url" style="width: 18px" />
                <div>{{ selectedOption.name_ru }}</div>
            </div>
        </ng-template>
        <ng-template let-country #item>
            <div class="flex items-center gap-2">
                <img [src]="country.flag_url"  style="width: 18px" />
                <div>{{ country.name_ru }}</div>
            </div>
        </ng-template>
      </p-select>
    </div>
  </div>

  <!-- Content -->
  <div class="admin-content-wrapper admin-table">
    <p-table
      [columns]="selectedColumns"
      [value]="filteredUsers"
      dataKey="id"
      [scrollable]="true"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 50, 100, 200]"
      [resizableColumns]="true"
      [reorderableColumns]="true"
      columnResizeMode="expand"
      styleClass="p-datatable-gridlines"
      scrollHeight="800px"
      [tableStyle]="{'min-width': '10rem'}">
   <ng-template #caption>
    <p-multiselect
        display="chip"
        [options]="cols"
        [(ngModel)]="selectedColumns"
        optionLabel="header"
        selectedItemsLabel="{0} columns selected"
        [style]="{'min-width': '200px'}"
        placeholder="Choose Columns"
        (onChange)="onColumnSelectionChange()"/>
    </ng-template>
    <ng-template #header let-columns>
      <tr>
          <th *ngFor="let col of columns" pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
              {{col.header}} <p-sortIcon [field]="col.field" />
          </th>

          <th style="width: 5rem"></th>
          <th style="width: 5rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-user let-columns="columns">
      <tr (click)="router.navigate(['/users/' + user.id])">
        <td *ngFor="let col of columns">
            @switch (col.field) {
              @case ('groups') {
                {{groupLabel(user.groups)}}
              }
              @case ('createdAt') {
                {{formatDate(user.createdAt)}}
              }
              @case ('lastActivity') {
                {{formatDate(user.lastActivity)}}
              }
              @case ('statuses') {
                {{statusLabel(user.statuses)}}
              }
              @case ('active') {
                {{user.active ? 'Да' : 'Нет'}}
              }
              @case ('confirmed') {
                {{user.confirmed ? 'Да' : 'Нет'}}
              }
              @case ('country') {
                {{findCountryByIsoCode(user.country)?.name_ru}}
              }
              @case ('avatar') {
                <img class="profile-avatar" style="width: 50px; height: 50px;" *ngIf="user.avatar" [src]="environment.serverUrl + '/upload/'  + user.avatar!.name" alt="">
              }
              @default {
                {{user[col.field]}}
              }
            }
        </td>
        <td>
          <i (click)="router.navigate(['/users/' + user.id])" class="pi pi-file-edit"></i>
        </td>
        <td>
          <i (click)="$event.stopPropagation();deleteUser(user.id)"  class="pi pi-trash"></i>
        </td>
    </tr>
  </ng-template>
      <ng-template #emptymessage>
          <tr>
              <td colspan="6">Нет данных</td>
          </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- Edit User Dialog -->
  <dialog #editDialog class="admin-modal">
    <div class="admin-modal-content">
      <h3 class="text-lg font-semibold mb-4">Редактировать пользователя</h3>
      <form [formGroup]="editForm" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <input type="checkbox" class="form-checkbox mr-2" formControlName="active">
            <label class="text-sm">Активность</label>
          </div>
          <div class="flex items-center">
            <input type="checkbox" class="form-checkbox mr-2" formControlName="confirmed">
            <label class="text-sm">Подтвержден</label>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="admin-filter-label">Имя</label>
            <input type="text" formControlName="firstName" class="form-input">
          </div>
          <div>
            <label class="admin-filter-label">Отчество</label>
            <input type="text" formControlName="middleName" class="form-input">
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="admin-filter-label">Фамилия</label>
            <input type="text" formControlName="lastName" class="form-input">
          </div>
          <div>
            <label class="admin-filter-label">Духовное имя</label>
            <input type="text" formControlName="spiritualName" class="form-input">
          </div>
        </div>

        <div>
          <label class="admin-filter-label">E-mail</label>
          <input type="text" formControlName="email" [disabled]="true" class="form-input">
        </div>

        <div>
          <label class="admin-filter-label">Группа</label>
          <select class="form-select" formControlName="groups" multiple>
            <option *ngFor="let group of groups" [value]="group.value">{{group.label}}</option>
          </select>
        </div>

        <div>
          <label class="admin-filter-label">Статус</label>
          <select class="form-select" formControlName="statuses" multiple>
            <option *ngFor="let status of statuses" [value]="status.value">{{status.label}}</option>
          </select>
        </div>
      </form>

      <div class="admin-modal-footer">
        <button class="btn btn-primary" (click)="saveUser()">Сохранить</button>
        <button class="btn btn-outline-secondary" (click)="closeDialog()">Закрыть</button>
      </div>
    </div>
  </dialog>
</div>
