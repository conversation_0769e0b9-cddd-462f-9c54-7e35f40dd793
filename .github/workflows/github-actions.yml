name: Deploy (dev, master)
on:
  push:
    branches:
      - dev
      - master
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json

      - name: Install deps & build admin
        run: |
          cd admin
          npm ci --force
          npm run build

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Upload admin dist to server (dev)
        if: github.ref == 'refs/heads/dev'
        run: |
          scp -o StrictHostKeyChecking=no -P 822 -r admin/dist alexander@**************:/home/<USER>/dev.sanatanadharma.world/admin/

      - name: Upload admin dist to server (master)
        if: github.ref == 'refs/heads/master'
        run: |
          scp -o StrictHostKeyChecking=no -P 822 -r admin/dist alexander@**************:/home/<USER>/sanatanadharma.world/admin/

      - name: Deploy to server (dev)
        if: github.ref == 'refs/heads/dev'
        run: |
          ssh -o StrictHostKeyChecking=no alexander@************** -p 822 << 'EOF'
          set -euo pipefail

          cd /home/<USER>/dev.sanatanadharma.world

          git fetch origin dev
          git reset --hard origin/dev

          podman-compose -f docker-compose.yml -f docker-compose.development.yml down || true
          podman-compose -f docker-compose.yml -f docker-compose.development.yml build
          podman-compose -f docker-compose.yml -f docker-compose.development.yml up -d

          podman image prune -f

          podman cp dev.sanatanadharma.world-client:/client/dist/client/. ./client/dist/

          EOF

      - name: Deploy to server (master)
        if: github.ref == 'refs/heads/master'
        run: |
          ssh -o StrictHostKeyChecking=no alexander@************** -p 822 << 'EOF'
          set -euo pipefail

          cd /home/<USER>/sanatanadharma.world

          git fetch origin master
          git reset --hard origin/master

          podman-compose -f docker-compose.yml down || true
          podman-compose -f docker-compose.yml build
          podman-compose -f docker-compose.yml up -d

          podman image prune -f

          podman cp sanatanadharma.world-client:/client/dist/client/. ./client/dist/
