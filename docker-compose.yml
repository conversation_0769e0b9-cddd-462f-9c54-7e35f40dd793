services:
  server:
    container_name: 'sanatanadharma.world-server'
    build:
      context: ./server
    env_file:
      - .env
    ports:
      - '9090:9090'
    networks:
      - internal
    depends_on:
      - postgresql
    healthcheck:
      test: ["CMD", "curl", "-f", "http://server:9015/api/client/translation/ru"]
      interval: 5s
      timeout: 5s
      retries: 10
    volumes:
      - ./server/upload:/server/upload

  # Angular SSR frontend
  client:
    container_name: 'sanatanadharma.world-client'
    build:
      context: ./client
      target: ${BUILD_TARGET:-production}
    env_file:
      - .env
    ports:
      - '9091:9091'
    depends_on:
      - server
    networks:
      - internal
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://server:9015/api/client/translation/ru"]
      interval: 5s
      timeout: 5s
      retries: 10

#  admin:
#    container_name: 'advayta_admin'
#    working_dir: '/admin'
#    env_file:
#      - .env
#    networks:
#      - internal
#    ports:
#      - '9016:9016'

  postgresql:
    container_name: 'sanatanadharma.world-postgres'
    image: postgres:17
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "advayta"]
      interval: 5s
      timeout: 10s
      retries: 10
    volumes:
      - postgresql-data:/var/lib/postgresql/data
      # - ./advayta-database.gz:/docker-entrypoint-initdb.d/init.sql.gz
    networks:
      - internal

networks:
  internal:

volumes:
  postgresql-data:
