import {
    Base<PERSON>ntity,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn
} from "typeorm";
import { ChatMessage } from "./ChatMessage";

@Entity('chat_sources')
export class ChatSource extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'varchar', length: 255 })
    title: string;

    @Column({ type: 'text' })
    content: string;

    @Column({ type: 'varchar', length: 500, nullable: true })
    url?: string;

    @Column({ type: 'text', nullable: true, name: 'highlighted_text' })
    highlightedText?: string;

    @ManyToOne(() => ChatMessage, message => message.sources, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'message_id' })
    message: ChatMessage;

    @Column({ name: 'message_id' })
    messageId: string;
}
