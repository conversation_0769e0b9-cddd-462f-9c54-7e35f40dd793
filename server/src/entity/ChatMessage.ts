import {
    BaseEntity,
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn
} from "typeorm"
import { Chat } from "./Chat"


export enum MessageRole {
    USER = 'user',
    ASSISTANT = 'assistant'
}

@Entity('chat_messages')
export class ChatMessage extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'text' })
    content: string;

    @Column({
        type: 'enum',
        enum: MessageRole,
        default: MessageRole.USER
    })
    role: MessageRole;

    @CreateDateColumn({ type: "timestamp" })
    timestamp: Date;

    @Column({ type: 'boolean', default: false, name: 'is_typing' })
    isTyping: boolean;

    @ManyToOne(() => Chat, chat => chat.messages, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'chat_id' })
    chat: Chat;

    @Column({ name: 'chat_id' })
    chatId: string;

    @OneToMany('ChatSource', 'message', { cascade: true })
    sources: any[];
}
