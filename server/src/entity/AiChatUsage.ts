import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import { User } from "./User"

@Entity('ai_chat_usage')
@Index(['userId', 'date'], { unique: true })
export class AiChatUsage extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @CreateDateColumn({ type: "timestamp", name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: "timestamp", name: 'updated_at' })
    updatedAt: Date;

    @Column({ type: 'date' })
    date: Date;

    @Column({ type: 'int', default: 0, name: 'chats_created' })
    chatsCreated: number;

    @Column({ type: 'int', default: 0, name: 'messages_sent' })
    messagesSent: number;

    @ManyToOne(() => User, { nullable: false, onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ name: 'user_id' })
    userId: number;
}
