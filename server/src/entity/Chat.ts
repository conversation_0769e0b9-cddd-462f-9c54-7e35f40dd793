import {
    BaseEntity,
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    <PERSON>inColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import { User } from "./User"


@Entity('chats')
export class Chat extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'varchar', length: 255 })
    title: string;

    @CreateDateColumn({ type: "timestamp", name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: "timestamp", name: 'updated_at' })
    updatedAt: Date;

    @Column({ type: 'boolean', default: false, name: 'is_pinned' })
    isPinned: boolean;

    @ManyToOne(() => User, { nullable: false })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ name: 'user_id' })
    userId: number;

    @OneToMany('ChatMessage', 'chat', { cascade: true })
    messages: any[];
}
