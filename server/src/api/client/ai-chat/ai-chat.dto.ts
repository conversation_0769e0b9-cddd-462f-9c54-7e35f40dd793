import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUrl } from 'class-validator'

export class SendMessageDto {
    @IsString()
    @IsNotEmpty()
    content: string;
}

export class UpdateChatDto {
    @IsOptional()
    @IsString()
    title?: string;

    @IsOptional()
    @IsBoolean()
    isPinned?: boolean;
}

export class FetchUrlDto {
    @IsString()
    @IsNotEmpty()
    @IsUrl()
    url: string;
}
