import { AiChatUsage } from '@/entity/AiChatUsage'
import { Chat } from '@/entity/Chat'
import { ChatMessage, MessageRole } from '@/entity/ChatMessage'
import { User } from '@/entity/User'
import { BadRequestException, Injectable } from '@nestjs/common'

export interface AiChatLimits {
    maxChats: number;
    maxMessagesPerDay: number;
    maxMessagesPerChat: number;
}

@Injectable()
export class AiChatLimitService {

    getUserLimits(user: User): AiChatLimits {
        const hasAiSubscription = this.hasAiAccess(user);
        
        if (hasAiSubscription) {
            return {
                maxChats: 10,
                maxMessagesPerDay: Infinity,
                maxMessagesPerChat: 100
            };
        } else {
            return {
                maxChats: 1,
                maxMessagesPerDay: 20,
                maxMessagesPerChat: Infinity 
            };
        }
    }

    hasAiAccess(user: User) {
        if (!user.subscriptions || user.subscriptions.length === 0) {
            return false;
        }

        const now = new Date();
        
        return user.subscriptions.some(subscription => {
            if (subscription.currentPeriodEnd && subscription.currentPeriodEnd < now) {
                return false;
            }

            const aiSubscriptionTypes = ['AI', 'FULL_ACCESS'];
            return aiSubscriptionTypes.includes(subscription.type);
        });
    }

    async canCreateChat(userId: number) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['subscriptions']
        });

        if (!user) {
            throw new BadRequestException('Пользователь не найден');
        }

        const limits = this.getUserLimits(user);
        
        const existingChatsCount = await Chat.count({
            where: { userId }
        });

        if (existingChatsCount >= limits.maxChats) {
            if (this.hasAiAccess(user)) {
                throw new BadRequestException(`Вы достигли максимального количества чатов (${limits.maxChats}).`);
            } else {
                throw new BadRequestException(`Вы можете создать только ${limits.maxChats} чат. Для создания большего количества чатов оформите подписку на AI-чат.`);
            }
        }
    }

    async canSendMessage(userId: number, chatId: string) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['subscriptions']
        });

        if (!user) {
            throw new BadRequestException('Пользователь не найден');
        }

        const limits = this.getUserLimits(user);
        
        if (limits.maxMessagesPerDay !== Infinity) {
            await this.checkDailyMessageLimit(userId, limits.maxMessagesPerDay);
        }

        if (limits.maxMessagesPerChat !== Infinity) {
            await this.checkChatMessageLimit(chatId, limits.maxMessagesPerChat);
        }
    }

    private async checkDailyMessageLimit(userId: number, maxMessages: number) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        let usage = await AiChatUsage.findOne({
            where: {
                userId,
                date: today
            }
        });

        if (!usage) {
            usage = AiChatUsage.create({
                userId,
                date: today,
                chatsCreated: 0,
                messagesSent: 0
            });
            await usage.save();
        }

        if (usage.messagesSent >= maxMessages) {
            throw new BadRequestException(`Вы достигли дневного лимита сообщений (${maxMessages}). Для увеличения лимита оформите подписку на AI-чат.`);
        }
    }

    private async checkChatMessageLimit(chatId: string, maxMessages: number) {
        const messagesCount = await ChatMessage.count({
            where: { 
                chatId,
                role: MessageRole.USER
            }
        });

        if (messagesCount >= maxMessages) {
            throw new BadRequestException(`Вы достигли максимального количества сообщений в этом чате (${maxMessages}).`);
        }
    }

    async recordChatCreation(userId: number) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        let usage = await AiChatUsage.findOne({
            where: {
                userId,
                date: today
            }
        });

        if (!usage) {
            usage = AiChatUsage.create({
                userId,
                date: today,
                chatsCreated: 1,
                messagesSent: 0
            });
        } else {
            usage.chatsCreated += 1;
        }

        await usage.save();
    }

    async recordMessageSent(userId: number) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        let usage = await AiChatUsage.findOne({
            where: {
                userId,
                date: today
            }
        });

        if (!usage) {
            usage = AiChatUsage.create({
                userId,
                date: today,
                chatsCreated: 0,
                messagesSent: 1
            });
        } else {
            usage.messagesSent += 1;
        }

        await usage.save();
    }

    async getTodayUsage(userId: number) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        return AiChatUsage.findOne({
            where: {
                userId,
                date: today
            }
        });
    }
}
