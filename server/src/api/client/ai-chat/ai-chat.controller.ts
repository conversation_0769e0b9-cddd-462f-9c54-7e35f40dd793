import { Auth } from '@/api/user/decorators/auth.decorator'
import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Req
} from '@nestjs/common'
import { FetchUrlDto, SendMessageDto, UpdateChatDto } from './ai-chat.dto'
import { AiChatService } from './ai-chat.service'

@Controller('client/ai-chat')
@Auth()
export class AiChatController {
    constructor(private readonly aiChatService: AiChatService) {}

    @Get()
    async getChats(@Req() req) {
        return this.aiChatService.getChats(req.user.id);
    }

    @Post()
    async createNewChat(@Req() req) {
        return this.aiChatService.createNewChat(req.user.id);
    }

    @Patch(':id')
    async updateChat(
        @Param('id') id: string,
        @Body() updates: UpdateChatDto,
        @Req() req
    ) {
        return this.aiChatService.updateChat(id, req.user.id, updates);
    }

    @Delete(':id')
    async deleteChat(@Param('id') id: string, @Req() req) {
        await this.aiChatService.deleteChat(id, req.user.id);
        return { success: true };
    }

    @Get(':id/messages')
    async getChatMessages(@Param('id') id: string, @Req() req) {
        return this.aiChatService.getChatMessages(id, req.user.id);
    }

    @Post(':id/messages')
    async sendMessage(
        @Param('id') id: string,
        @Body() dto: SendMessageDto,
        @Req() req
    ) {
        return this.aiChatService.sendMessage(id, req.user.id, dto.content, req.user.email);
    }

    @Post('fetch-url')
    async fetchUrl(@Body() dto: FetchUrlDto) {
        return this.aiChatService.fetchUrl(dto.url);
    }
}
