import { Backup } from '@/entity/Backup'
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { BackupsController } from './backups.controller'
import { BackupsService } from './backups.service'

@Module({
  imports: [TypeOrmModule.forFeature([Backup]), HttpModule],
  controllers: [BackupsController],
  providers: [BackupsService],
})
export class BackupsModule {}
